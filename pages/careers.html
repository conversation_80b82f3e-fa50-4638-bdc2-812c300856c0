<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Career Opportunities - DevGlobal</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Additional styles specific to careers page */
        .positions {
            padding: 100px 0;
            background-color: var(--bg-secondary);
        }
        
        .position-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        
        .position-card {
            background-color: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            padding: 40px 30px;
            text-align: center;
            box-shadow: var(--shadow-md);
            transition: var(--transition-medium);
        }
        
        .position-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-lg);
        }
        
        .card-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--text-light);
            font-size: 1.8rem;
        }
        
        .position-card h3 {
            margin-bottom: 15px;
        }
        
        .position-card p {
            margin-bottom: 25px;
        }
        
        .apply-button {
            display: inline-block;
            padding: 12px 30px;
            border-radius: var(--border-radius-round);
            background-color: var(--primary);
            color: var(--text-light);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-fast);
        }
        
        .apply-button:hover {
            background-color: var(--secondary);
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }
        
        .benefits {
            padding: 100px 0;
        }
        
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        
        .benefit-card {
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius-lg);
            padding: 30px;
            text-align: center;
            box-shadow: var(--shadow-sm);
            transition: var(--transition-medium);
        }
        
        .benefit-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
        }
        
        .benefit-icon {
            width: 60px;
            height: 60px;
            background-color: rgba(67, 97, 238, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--primary);
            font-size: 1.5rem;
        }
        
        .benefit-card h3 {
            margin-bottom: 15px;
        }
        
        .application-process {
            padding: 100px 0;
            background-color: var(--bg-secondary);
        }
        
        .process-steps {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-top: 50px;
        }
        
        .process-step {
            flex: 1;
            min-width: 250px;
            max-width: 300px;
            text-align: center;
            position: relative;
        }
        
        .step-number {
            width: 50px;
            height: 50px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--text-light);
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        .process-step h3 {
            margin-bottom: 15px;
        }
        
        .process-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 25px;
            right: -30px;
            width: 30px;
            height: 2px;
            background-color: var(--primary);
        }
        
        @media (max-width: 768px) {
            .process-step:not(:last-child)::after {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="container">
            <div class="logo">
                <h1><a href="../index.html" style="text-decoration: none; color: inherit;">DevGlobal</a></h1>
            </div>
            <nav>
                <div class="nav-toggle" id="navToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul class="nav-links">
                    <li><a href="../index.html">Home</a></li>
                    <li><a href="services.html">Services</a></li>
                    <li><a href="hosting.html">Hosting</a></li>
                    <li><a href="portfolio.html">Portfolio</a></li>
                    <li><a href="careers.html" class="active">Careers</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Page Banner -->
    <section class="page-banner">
        <div class="container">
            <div class="banner-content" data-aos="fade-up">
                <h1>Join Our Team</h1>
                <p>We're looking for exceptional talent to help us build amazing things</p>
            </div>
        </div>
    </section>

    <!-- Open Positions Section -->
    <section class="positions">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Open Positions</h2>
                <p>Explore exciting career opportunities at DevGlobal</p>
            </div>
            <div class="position-cards">
                <div class="position-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Web Developers</h3>
                    <p>Build innovative web solutions for our clients using the latest technologies and frameworks.</p>
                    <a href="#" class="apply-button" data-position="web-developer">Apply Now</a>
                </div>
                
                <div class="position-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-icon">
                        <i class="fas fa-pencil-ruler"></i>
                    </div>
                    <h3>UI Designers</h3>
                    <p>Craft intuitive interfaces that delight users and create exceptional user experiences.</p>
                    <a href="#" class="apply-button" data-position="ui-designer">Apply Now</a>
                </div>
                
                <div class="position-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>App Developers</h3>
                    <p>Create native and cross-platform mobile applications that deliver exceptional user experiences.</p>
                    <a href="#" class="apply-button" data-position="app-developer">Apply Now</a>
                </div>
                
                <div class="position-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>Digital Marketers</h3>
                    <p>Drive growth through strategic digital marketing campaigns and data-driven strategies.</p>
                    <a href="#" class="apply-button" data-position="digital-marketer">Apply Now</a>
                </div>                
                <div class="position-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="card-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3>Video Editors</h3>
                    <p>Create compelling video content that tells stories and engages audiences across multiple platforms.</p>
                    <a href="#" class="apply-button" data-position="video-editor">Apply Now</a>
                </div>
                
                <div class="position-card" data-aos="fade-up" data-aos-delay="600">
                    <div class="card-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Graphic Designers</h3>
                    <p>Design stunning visual assets that communicate brand messages and captivate target audiences.</p>
                    <a href="#" class="apply-button" data-position="graphic-designer">Apply Now</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Why Work With Us</h2>
                <p>We offer a range of benefits to help you thrive personally and professionally</p>
            </div>
            <div class="benefits-grid">
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="benefit-icon">
                        <i class="fas fa-laptop-house"></i>
                    </div>
                    <h3>Remote Work</h3>
                    <p>Enjoy the flexibility of working from anywhere with our remote-first approach.</p>
                </div>
                
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="benefit-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3>Learning Budget</h3>
                    <p>Access to courses, conferences, and resources to help you grow your skills.</p>
                </div>
                
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="benefit-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Collaborative Culture</h3>
                    <p>Work with talented professionals in a supportive and inclusive environment.</p>
                </div>
                
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="benefit-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Career Growth</h3>
                    <p>Clear paths for advancement and opportunities to lead exciting projects.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Application Process -->
    <section class="application-process">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Our Hiring Process</h2>
                <p>What to expect when you apply for a position at DevGlobal</p>
            </div>
            <div class="process-steps">
                <div class="process-step" data-aos="fade-up" data-aos-delay="100">
                    <div class="step-number">1</div>
                    <h3>Application</h3>
                    <p>Submit your resume and portfolio through our online application system.</p>
                </div>
                
                <div class="process-step" data-aos="fade-up" data-aos-delay="200">
                    <div class="step-number">2</div>
                    <h3>Initial Interview</h3>
                    <p>A video call to discuss your experience, skills, and career goals.</p>
                </div>
                
                <div class="process-step" data-aos="fade-up" data-aos-delay="300">
                    <div class="step-number">3</div>
                    <h3>Technical Assessment</h3>
                    <p>Complete a practical task related to the position you're applying for.</p>
                </div>
                
                <div class="process-step" data-aos="fade-up" data-aos-delay="400">
                    <div class="step-number">4</div>
                    <h3>Final Interview</h3>
                    <p>Meet with the team to discuss your assessment and explore cultural fit.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>DevGlobal</h2>
                    <p>Web Development & Digital Services</p>
                </div>
                <div class="footer-links">
                    <div class="footer-column">
                        <h3>Services</h3>
                        <ul>
                            <li><a href="services.html#web-development">Web Development</a></li>
                            <li><a href="hosting.html">Web Hosting</a></li>
                            <li><a href="services.html#app-development">App Development</a></li>
                            <li><a href="services.html#graphic-design">Graphic Design</a></li>
                            <li><a href="services.html#video-editing">Video Editing</a></li>
                            <li><a href="services.html#digital-marketing">Digital Marketing</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h3>Company</h3>
                        <ul>
                            <li><a href="about.html">About Us</a></li>
                            <li><a href="portfolio.html">Portfolio</a></li>
                            <li><a href="careers.html">Careers</a></li>
                            <li><a href="contact.html">Contact</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h3>Contact</h3>
                        <ul class="contact-info">
                            <li><i class="fas fa-map-marker-alt"></i> 123 Web Street, Digital City</li>
                            <li><i class="fas fa-phone"></i> +91 1234567890</li>
                            <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 DevGlobal. All rights reserved.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
</body>
</html>