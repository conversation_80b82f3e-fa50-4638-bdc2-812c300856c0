<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Portfolio - DevGlobal</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Additional styles specific to portfolio page */
        .portfolio-section {
            padding: 100px 0;
        }
        
        .portfolio-filters {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 50px;
        }
        
        .portfolio-filter {
            padding: 8px 20px;
            background-color: var(--bg-secondary);
            border-radius: 30px;
            cursor: pointer;
            transition: var(--transition-fast);
            border: none;
            color: var(--text-secondary);
            font-family: 'Poppins', sans-serif;
            font-size: 0.9rem;
        }
        
        .portfolio-filter:hover,
        .portfolio-filter.active {
            background-color: var(--primary);
            color: white;
        }
        
        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
        }
        
        .portfolio-item {
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            position: relative;
            box-shadow: var(--shadow-md);
            transition: var(--transition-medium);
            height: 300px;
        }
        
        .portfolio-item:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-lg);
        }
        
        .portfolio-image {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .portfolio-item:hover .portfolio-image img {
            transform: scale(1.05);
        }
        
        .portfolio-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            padding: 25px;
            opacity: 0;
            transition: var(--transition-medium);
        }
        
        .portfolio-item:hover .portfolio-overlay {
            opacity: 1;
        }
        
        .portfolio-overlay h3 {
            color: white;
            margin-bottom: 5px;
        }
        
        .portfolio-overlay p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            font-size: 0.9rem;
        }
        
        .portfolio-overlay .btn {
            align-self: flex-start;
            padding: 8px 20px;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .portfolio-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="container">
            <div class="logo">
                <h1><a href="../index.html" style="text-decoration: none; color: inherit;">DevGlobal</a></h1>
            </div>
            <nav>
                <div class="nav-toggle" id="navToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul class="nav-links">
                    <li><a href="../index.html">Home</a></li>
                    <li><a href="services.html">Services</a></li>
                    <li><a href="hosting.html">Hosting</a></li>
                    <li><a href="portfolio.html" class="active">Portfolio</a></li>
                    <li><a href="careers.html">Careers</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Page Banner -->
    <section class="page-banner">
        <div class="container">
            <div class="banner-content" data-aos="fade-up">
                <h1>Our Portfolio</h1>
                <p>Explore our latest projects and success stories</p>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section class="portfolio-section">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Featured Projects</h2>
                <p>A showcase of our best work across various industries</p>
            </div>
            
            <div class="portfolio-filters" data-aos="fade-up">
                <button class="portfolio-filter active" data-filter="all">All</button>
                <button class="portfolio-filter" data-filter="web">Web Development</button>
                <button class="portfolio-filter" data-filter="app">App Development</button>
                <button class="portfolio-filter" data-filter="ecommerce">E-commerce</button>
                <button class="portfolio-filter" data-filter="branding">Branding</button>
            </div>
            
            <div class="portfolio-grid">
                <!-- Web Development Projects -->
                <div class="portfolio-item" data-category="web" data-aos="fade-up" data-aos-delay="100">
                    <div class="portfolio-image">
                        <img src="https://placehold.co/600x400" alt="Modern Business Website">
                    </div>
                    <div class="portfolio-overlay">
                        <h3>Modern Business Website</h3>
                        <p>A responsive corporate website with custom CMS integration</p>
                        <a href="#" class="btn btn-primary">View Project</a>
                    </div>
                </div>
                
                <div class="portfolio-item" data-category="web" data-aos="fade-up" data-aos-delay="200">
                    <div class="portfolio-image">
                        <img src="https://placehold.co/600x400" alt="Educational Platform">
                    </div>
                    <div class="portfolio-overlay">
                        <h3>Educational Platform</h3>
                        <p>An interactive learning platform with course management system</p>
                        <a href="#" class="btn btn-primary">View Project</a>
                    </div>
                </div>
                
                <!-- App Development Projects -->
                <div class="portfolio-item" data-category="app" data-aos="fade-up" data-aos-delay="300">
                    <div class="portfolio-image">
                        <img src="https://placehold.co/600x400" alt="Fitness Tracking App">
                    </div>
                    <div class="portfolio-overlay">
                        <h3>Fitness Tracking App</h3>
                        <p>A mobile application for tracking workouts and nutrition</p>
                        <a href="#" class="btn btn-primary">View Project</a>
                    </div>
                </div>
                
                <div class="portfolio-item" data-category="app" data-aos="fade-up" data-aos-delay="400">
                    <div class="portfolio-image">
                        <img src="https://placehold.co/600x400" alt="Food Delivery App">
                    </div>
                    <div class="portfolio-overlay">
                        <h3>Food Delivery App</h3>
                        <p>A cross-platform app connecting restaurants with customers</p>
                        <a href="#" class="btn btn-primary">View Project</a>
                    </div>
                </div>
                
                <!-- E-commerce Projects -->
                <div class="portfolio-item" data-category="ecommerce" data-aos="fade-up" data-aos-delay="100">
                    <div class="portfolio-image">
                        <img src="https://placehold.co/600x400" alt="Fashion E-commerce">
                    </div>
                    <div class="portfolio-overlay">
                        <h3>Fashion E-commerce</h3>
                        <p>A complete online shopping solution for a fashion brand</p>
                        <a href="#" class="btn btn-primary">View Project</a>
                    </div>
                </div>
                
                <div class="portfolio-item" data-category="ecommerce" data-aos="fade-up" data-aos-delay="200">
                    <div class="portfolio-image">
                        <img src="https://placehold.co/600x400" alt="Electronics Store">
                    </div>
                    <div class="portfolio-overlay">
                        <h3>Electronics Store</h3>
                        <p>A feature-rich e-commerce platform with advanced filtering</p>
                        <a href="#" class="btn btn-primary">View Project</a>
                    </div>
                </div>
                
                <!-- Branding Projects -->
                <div class="portfolio-item" data-category="branding" data-aos="fade-up" data-aos-delay="300">
                    <div class="portfolio-image">
                        <img src="https://placehold.co/600x400" alt="Cafe Branding">
                    </div>
                    <div class="portfolio-overlay">
                        <h3>Cafe Branding</h3>
                        <p>Complete brand identity design for a modern coffee shop</p>
                        <a href="#" class="btn btn-primary">View Project</a>
                    </div>
                </div>
                
                <div class="portfolio-item" data-category="branding" data-aos="fade-up" data-aos-delay="400">
                    <div class="portfolio-image">
                        <img src="https://placehold.co/600x400" alt="Tech Startup Branding">
                    </div>
                    <div class="portfolio-overlay">
                        <h3>Tech Startup Branding</h3>
                        <p>Brand strategy and visual identity for an innovative startup</p>
                        <a href="#" class="btn btn-primary">View Project</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Client Testimonials -->
    <section class="testimonials">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Client Testimonials</h2>
                <p>What our clients say about working with us</p>
            </div>
            
            <div class="testimonials-slider" data-aos="fade-up">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"DevGlobal transformed our online presence with a stunning website that perfectly captures our brand. Their team was professional, responsive, and delivered beyond our expectations."</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://placehold.co/100x100" alt="Client">
                        <div class="author-info">
                            <h4>Sarah Johnson</h4>
                            <p>CEO, Fashion Forward</p>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"The mobile app developed by DevGlobal has significantly improved our customer engagement and sales. Their attention to detail and user experience expertise made all the difference."</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://placehold.co/100x100" alt="Client">
                        <div class="author-info">
                            <h4>Michael Chen</h4>
                            <p>Founder, QuickBite</p>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"We've been using DevGlobal's hosting services for over two years now, and the reliability and support have been exceptional. Our website has never been faster or more secure."</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://placehold.co/100x100" alt="Client">
                        <div class="author-info">
                            <h4>Priya Sharma</h4>
                            <p>Director, TechSolutions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>DevGlobal</h2>
                    <p>Web Development & Digital Services</p>
                </div>
                <div class="footer-links">
                    <div class="footer-column">
                        <h3>Services</h3>
                        <ul>
                            <li><a href="services.html#video-editing">Video Editing</a></li>
                            <li><a href="hosting.html">Web Hosting</a></li>
                            <li><a href="services.html#app-development">App Development</a></li>
                            <li><a href="services.html#graphic-design">Graphic Design</a></li>
                            <li><a href="services.html#digital-marketing">Digital Marketing</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h3>Company</h3>
                        <ul>
                            <li><a href="about.html">About Us</a></li>
                            <li><a href="portfolio.html">Portfolio</a></li>
                            <li><a href="careers.html">Careers</a></li>
                            <li><a href="contact.html">Contact</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h3>Contact</h3>
                        <ul class="contact-info">
                            <li><i class="fas fa-map-marker-alt"></i> 123 Web Street, Digital City</li>
                            <li><i class="fas fa-phone"></i> +91 1234567890</li>
                            <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 DevGlobal. All rights reserved.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
    <script>
        // Portfolio filtering functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filters = document.querySelectorAll('.portfolio-filter');
            const items = document.querySelectorAll('.portfolio-item');
            
            filters.forEach(filter => {
                filter.addEventListener('click', function() {
                    // Update active filter
                    filters.forEach(f => f.classList.remove('active'));
                    this.classList.add('active');
                    
                    const filterValue = this.getAttribute('data-filter');
                    
                    // Filter items
                    items.forEach(item => {
                        if (filterValue === 'all' || item.getAttribute('data-category') === filterValue) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>