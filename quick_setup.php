<?php
/**
 * Quick Setup Script for DevGlobal
 * Simplified setup that creates database and admin user directly
 */

$error = '';
$success = '';
$step = 1;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'setup_database') {
        // Database setup
        $host = $_POST['db_host'] ?? 'localhost';
        $name = $_POST['db_name'] ?? 'devglobal_db';
        $user = $_POST['db_user'] ?? 'root';
        $pass = $_POST['db_pass'] ?? '';
        
        try {
            // Test connection and create database
            $dsn = "mysql:host=$host;charset=utf8mb4";
            $pdo = new PDO($dsn, $user, $pass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Connect to the specific database
            $dsn2 = "mysql:host=$host;dbname=$name;charset=utf8mb4";
            $pdo2 = new PDO($dsn2, $user, $pass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Import database schema
            $sql = file_get_contents('database/devglobal_database.sql');
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                    $pdo2->exec($statement);
                }
            }
            
            // Create admin user
            $adminUsername = $_POST['admin_username'] ?? 'admin';
            $adminEmail = $_POST['admin_email'] ?? '<EMAIL>';
            $adminPassword = $_POST['admin_password'] ?? 'admin123';
            $adminFullName = $_POST['admin_full_name'] ?? 'Admin User';
            
            $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
            
            $stmt = $pdo2->prepare("INSERT INTO admin_users (username, email, password_hash, full_name, role) VALUES (?, ?, ?, ?, 'super_admin')");
            $stmt->execute([$adminUsername, $adminEmail, $hashedPassword, $adminFullName]);
            
            // Update config file with database credentials
            $configTemplate = "<?php
/**
 * Database Configuration for DevGlobal
 */

// Database Configuration
define('DB_HOST', '$host');
define('DB_NAME', '$name');
define('DB_USER', '$user');
define('DB_PASS', '" . addslashes($pass) . "');
define('DB_CHARSET', 'utf8mb4');

// Database Connection Class
class Database {
    private static \$instance = null;
    private \$connection;
    
    private function __construct() {
        try {
            \$dsn = \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=\" . DB_CHARSET;
            \$options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            \$this->connection = new PDO(\$dsn, DB_USER, DB_PASS, \$options);
        } catch (PDOException \$e) {
            error_log(\"Database connection failed: \" . \$e->getMessage());
            die(\"Database connection failed. Please check your configuration.\");
        }
    }
    
    public static function getInstance() {
        if (self::\$instance === null) {
            self::\$instance = new self();
        }
        return self::\$instance;
    }
    
    public function getConnection() {
        return \$this->connection;
    }
    
    private function __clone() {}
    public function __wakeup() {}
}

// Helper function to get database connection
function getDB() {
    return Database::getInstance()->getConnection();
}

// Helper functions (keeping existing ones)
function executeQuery(\$sql, \$params = []) {
    try {
        \$db = getDB();
        \$stmt = \$db->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt;
    } catch (PDOException \$e) {
        error_log(\"Query execution failed: \" . \$e->getMessage());
        return false;
    }
}

function fetchOne(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    return \$stmt ? \$stmt->fetch() : false;
}

function fetchAll(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    return \$stmt ? \$stmt->fetchAll() : false;
}

function insertData(\$table, \$data) {
    try {
        \$db = getDB();
        \$columns = implode(', ', array_keys(\$data));
        \$placeholders = ':' . implode(', :', array_keys(\$data));
        \$sql = \"INSERT INTO {\$table} ({\$columns}) VALUES ({\$placeholders})\";
        \$stmt = \$db->prepare(\$sql);
        foreach (\$data as \$key => \$value) {
            \$stmt->bindValue(\":\$key\", \$value);
        }
        \$stmt->execute();
        return \$db->lastInsertId();
    } catch (PDOException \$e) {
        error_log(\"Insert failed: \" . \$e->getMessage());
        return false;
    }
}

function updateData(\$table, \$data, \$where, \$whereParams = []) {
    try {
        \$db = getDB();
        \$setParts = [];
        foreach (array_keys(\$data) as \$key) {
            \$setParts[] = \"{\$key} = :{\$key}\";
        }
        \$setClause = implode(', ', \$setParts);
        \$sql = \"UPDATE {\$table} SET {\$setClause} WHERE {\$where}\";
        \$stmt = \$db->prepare(\$sql);
        foreach (\$data as \$key => \$value) {
            \$stmt->bindValue(\":\$key\", \$value);
        }
        foreach (\$whereParams as \$key => \$value) {
            \$stmt->bindValue(\":\$key\", \$value);
        }
        return \$stmt->execute();
    } catch (PDOException \$e) {
        error_log(\"Update failed: \" . \$e->getMessage());
        return false;
    }
}

function deleteData(\$table, \$where, \$whereParams = []) {
    try {
        \$db = getDB();
        \$sql = \"DELETE FROM {\$table} WHERE {\$where}\";
        \$stmt = \$db->prepare(\$sql);
        foreach (\$whereParams as \$key => \$value) {
            \$stmt->bindValue(\":\$key\", \$value);
        }
        return \$stmt->execute();
    } catch (PDOException \$e) {
        error_log(\"Delete failed: \" . \$e->getMessage());
        return false;
    }
}

function sanitizeInput(\$input) {
    return htmlspecialchars(trim(\$input), ENT_QUOTES, 'UTF-8');
}

function isValidEmail(\$email) {
    return filter_var(\$email, FILTER_VALIDATE_EMAIL) !== false;
}

function generateRandomString(\$length = 32) {
    return bin2hex(random_bytes(\$length / 2));
}

function hashPassword(\$password) {
    return password_hash(\$password, PASSWORD_DEFAULT);
}

function verifyPassword(\$password, \$hash) {
    return password_verify(\$password, \$hash);
}

try {
    \$testConnection = getDB();
    if (!\$testConnection) {
        throw new Exception(\"Failed to establish database connection\");
    }
} catch (Exception \$e) {
    error_log(\"Database configuration error: \" . \$e->getMessage());
}
?>";

            file_put_contents('config/database.php', $configTemplate);
            
            // Mark setup as complete
            file_put_contents('config/setup_complete.txt', date('Y-m-d H:i:s'));
            
            $success = 'Setup completed successfully! Database created, tables imported, and admin user created.';
            $step = 2;
            
        } catch (Exception $e) {
            $error = 'Setup failed: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Setup - DevGlobal</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .setup-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #4361ee, #7209b7);
            padding: 20px;
        }
        
        .setup-box {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 600px;
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .setup-header h1 {
            color: #4361ee;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #4361ee;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: 500;
        }
        
        .btn-primary {
            background: #4361ee;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3651d4;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-box">
            <div class="setup-header">
                <h1><i class="fas fa-rocket"></i> DevGlobal Quick Setup</h1>
                <p>One-step setup for your DevGlobal website</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <form method="POST">
                    <input type="hidden" name="action" value="setup_database">
                    
                    <h3>Database Configuration</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="db_host">Database Host</label>
                            <input type="text" id="db_host" name="db_host" class="form-control" value="localhost" required>
                        </div>
                        <div class="form-group">
                            <label for="db_name">Database Name</label>
                            <input type="text" id="db_name" name="db_name" class="form-control" value="devglobal_db" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="db_user">Database Username</label>
                            <input type="text" id="db_user" name="db_user" class="form-control" value="root" required>
                        </div>
                        <div class="form-group">
                            <label for="db_pass">Database Password</label>
                            <input type="password" id="db_pass" name="db_pass" class="form-control" placeholder="Leave empty if no password">
                        </div>
                    </div>
                    
                    <h3 style="margin-top: 30px;">Admin User</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="admin_username">Admin Username</label>
                            <input type="text" id="admin_username" name="admin_username" class="form-control" value="admin" required>
                        </div>
                        <div class="form-group">
                            <label for="admin_email">Admin Email</label>
                            <input type="email" id="admin_email" name="admin_email" class="form-control" value="<EMAIL>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="admin_password">Admin Password</label>
                            <input type="password" id="admin_password" name="admin_password" class="form-control" value="admin123" required>
                        </div>
                        <div class="form-group">
                            <label for="admin_full_name">Full Name</label>
                            <input type="text" id="admin_full_name" name="admin_full_name" class="form-control" value="Admin User">
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 20px;">
                        <i class="fas fa-magic"></i> Setup Everything Now!
                    </button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <h3>Setup Complete!</h3>
                <p>Your DevGlobal website has been set up successfully.</p>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h4>Login Credentials:</h4>
                    <p><strong>Username:</strong> <?php echo htmlspecialchars($_POST['admin_username'] ?? 'admin'); ?></p>
                    <p><strong>Password:</strong> <?php echo htmlspecialchars($_POST['admin_password'] ?? 'admin123'); ?></p>
                    <p><strong>Email:</strong> <?php echo htmlspecialchars($_POST['admin_email'] ?? '<EMAIL>'); ?></p>
                </div>
                
                <div style="display: flex; gap: 15px; margin-top: 30px;">
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home"></i> View Website
                    </a>
                    <a href="admin/login.php" class="btn btn-success">
                        <i class="fas fa-sign-in-alt"></i> Admin Login
                    </a>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                <p style="margin: 0; color: #666; font-size: 14px;">
                    Having issues? Try the <a href="setup.php">step-by-step setup</a> or <a href="test_connection.php">test your connection</a>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
