<?php
/**
 * Main Configuration File for DevGlobal
 * 
 * This file contains all site-wide configuration settings
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
require_once __DIR__ . '/database.php';

// Site Configuration
define('SITE_URL', 'http://localhost/devglobal'); // Change this to your domain
define('ADMIN_URL', SITE_URL . '/admin');
define('API_URL', SITE_URL . '/api');

// File Upload Configuration
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('UPLOAD_URL', SITE_URL . '/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx']);

// Email Configuration
define('SMTP_HOST', 'localhost'); // Change to your SMTP server
define('SMTP_PORT', 587);
define('SMTP_USERNAME', ''); // Your SMTP username
define('SMTP_PASSWORD', ''); // Your SMTP password
define('SMTP_ENCRYPTION', 'tls'); // tls or ssl
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'DevGlobal');
define('ADMIN_EMAIL', '<EMAIL>');

// Security Configuration
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour in seconds
define('CSRF_TOKEN_EXPIRY', 1800); // 30 minutes
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Pagination Configuration
define('ITEMS_PER_PAGE', 10);
define('ADMIN_ITEMS_PER_PAGE', 20);

// Error Reporting (set to false in production)
define('DEBUG_MODE', true);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Helper Functions

/**
 * Get site settings from database
 */
function getSiteSettings() {
    static $settings = null;
    
    if ($settings === null) {
        $settings = fetchOne("SELECT * FROM site_settings WHERE id = 1");
        if (!$settings) {
            // Return default settings if none found
            $settings = [
                'site_name' => 'DevGlobal',
                'site_description' => 'Professional web development and digital services',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+91 1234567890',
                'contact_address' => '123 Web Street, Digital City',
                'hero_title' => 'Transform Your Digital Presence',
                'hero_subtitle' => 'Web Development, Hosting & Digital Services',
                'hero_description' => 'We help businesses grow through innovative web development, reliable hosting, and comprehensive digital solutions.'
            ];
        }
    }
    
    return $settings;
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || 
        (time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_EXPIRY) {
        $_SESSION['csrf_token'] = generateRandomString(32);
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && 
           isset($_SESSION['csrf_token_time']) &&
           (time() - $_SESSION['csrf_token_time']) <= CSRF_TOKEN_EXPIRY &&
           hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check if user is logged in as admin
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && 
           isset($_SESSION['admin_login_time']) &&
           (time() - $_SESSION['admin_login_time']) < ADMIN_SESSION_TIMEOUT;
}

/**
 * Get current admin user data
 */
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return false;
    }
    
    return fetchOne("SELECT id, username, email, full_name, role FROM admin_users WHERE id = :id AND is_active = 1", 
                   ['id' => $_SESSION['admin_id']]);
}

/**
 * Redirect to login if not authenticated
 */
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        // Check if we're already in admin directory
        $currentPath = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($currentPath, '/admin/') !== false) {
            // We're in admin directory, redirect to login.php
            header('Location: login.php');
        } else {
            // We're outside admin directory, redirect to admin/login.php
            header('Location: admin/login.php');
        }
        exit;
    }

    // Update last activity time
    $_SESSION['admin_login_time'] = time();
}

/**
 * Log admin activity
 */
function logAdminActivity($action, $details = '') {
    if (isAdminLoggedIn()) {
        $data = [
            'admin_id' => $_SESSION['admin_id'],
            'action' => $action,
            'details' => $details,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        // You can create an admin_logs table if you want to track activities
        // insertData('admin_logs', $data);
    }
}

/**
 * Send email notification
 */
function sendEmail($to, $subject, $message, $isHTML = true) {
    // Basic email sending - you can integrate PHPMailer for better functionality
    $headers = [
        'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
        'Reply-To: ' . FROM_EMAIL,
        'X-Mailer: PHP/' . phpversion()
    ];
    
    if ($isHTML) {
        $headers[] = 'Content-Type: text/html; charset=UTF-8';
    } else {
        $headers[] = 'Content-Type: text/plain; charset=UTF-8';
    }
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * Upload file with validation
 */
function uploadFile($file, $allowedTypes = null, $uploadDir = null) {
    if (!$uploadDir) {
        $uploadDir = UPLOAD_DIR;
    }
    
    if (!$allowedTypes) {
        $allowedTypes = array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_DOCUMENT_TYPES);
    }
    
    // Create upload directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Validate file
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'error' => 'File upload error'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'error' => 'File too large'];
    }
    
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, $allowedTypes)) {
        return ['success' => false, 'error' => 'File type not allowed'];
    }
    
    // Generate unique filename
    $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;
    
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return [
            'success' => true, 
            'filename' => $fileName,
            'filepath' => $filePath,
            'url' => UPLOAD_URL . $fileName
        ];
    }
    
    return ['success' => false, 'error' => 'Failed to move uploaded file'];
}

/**
 * Format currency
 */
function formatCurrency($amount, $currency = '₹') {
    return $currency . number_format($amount, 2);
}

/**
 * Format date
 */
function formatDate($date, $format = 'M d, Y') {
    return date($format, strtotime($date));
}

/**
 * Truncate text
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . $suffix;
}

/**
 * Generate order number
 */
function generateOrderNumber() {
    return 'ORD-' . date('Y') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

/**
 * JSON response helper
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Redirect helper
 */
function redirect($url, $statusCode = 302) {
    header("Location: $url", true, $statusCode);
    exit;
}

// Initialize upload directory
if (!is_dir(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}
?>
