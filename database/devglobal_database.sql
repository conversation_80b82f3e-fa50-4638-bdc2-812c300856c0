-- DevGlobal Database Schema
-- MySQL Database Structure for Complete Website Management

CREATE DATABASE IF NOT EXISTS devglobal_db;
USE devglobal_db;

-- Site Settings Table
CREATE TABLE site_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    site_name VARCHAR(255) NOT NULL DEFAULT 'DevGlobal',
    site_description TEXT,
    site_logo VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    contact_address TEXT,
    social_facebook VARCHAR(255),
    social_twitter VARCHAR(255),
    social_instagram VARCHAR(255),
    social_linkedin VARCHAR(255),
    hero_title VARCHAR(255),
    hero_subtitle VARCHAR(255),
    hero_description TEXT,
    hero_image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Services Table
CREATE TABLE services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    image VARCHAR(255),
    features TEXT, -- JSON format for features list
    price DECIMAL(10,2),
    price_type ENUM('one-time', 'monthly', 'yearly') DEFAULT 'one-time',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Hosting Plans Table
CREATE TABLE hosting_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    plan_name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
    features TEXT, -- JSON format for features list
    is_popular BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Development Plans Table
CREATE TABLE development_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    plan_name VARCHAR(255) NOT NULL,
    building_cost DECIMAL(10,2),
    monthly_cost DECIMAL(10,2),
    features TEXT, -- JSON format for features list
    is_popular BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Portfolio Items Table
CREATE TABLE portfolio_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image VARCHAR(255),
    category VARCHAR(100),
    client_name VARCHAR(255),
    project_url VARCHAR(255),
    technologies TEXT, -- JSON format for tech stack
    completion_date DATE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Job Positions Table
CREATE TABLE job_positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    department VARCHAR(100),
    location VARCHAR(255),
    job_type ENUM('full-time', 'part-time', 'contract', 'internship') DEFAULT 'full-time',
    description TEXT,
    requirements TEXT, -- JSON format for requirements list
    responsibilities TEXT, -- JSON format for responsibilities list
    salary_range VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact Messages Table
CREATE TABLE contact_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Orders Table
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(50),
    service_type ENUM('hosting', 'development', 'service') NOT NULL,
    service_id INT,
    plan_details TEXT, -- JSON format for plan details
    total_amount DECIMAL(10,2),
    status ENUM('pending', 'confirmed', 'in-progress', 'completed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Job Applications Table
CREATE TABLE job_applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_id INT NOT NULL,
    applicant_name VARCHAR(255) NOT NULL,
    applicant_email VARCHAR(255) NOT NULL,
    applicant_phone VARCHAR(50),
    resume_file VARCHAR(255),
    cover_letter TEXT,
    experience_years INT,
    current_salary VARCHAR(100),
    expected_salary VARCHAR(100),
    status ENUM('new', 'reviewed', 'shortlisted', 'interviewed', 'hired', 'rejected') DEFAULT 'new',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES job_positions(id) ON DELETE CASCADE
);

-- Admin Users Table
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    role ENUM('super_admin', 'admin', 'editor') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- GPS Products Table (for the GPS tracker section)
CREATE TABLE gps_products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_name VARCHAR(255) NOT NULL,
    description TEXT,
    image VARCHAR(255),
    features TEXT, -- JSON format for features list
    support_features TEXT, -- JSON format for support features
    price DECIMAL(10,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert Default Data
INSERT INTO site_settings (site_name, site_description, contact_email, contact_phone, contact_address, hero_title, hero_subtitle, hero_description) VALUES
('DevGlobal', 'Professional web development and digital services agency', '<EMAIL>', '+91 1234567890', '123 Web Street, Digital City', 'Transform Your Digital Presence', 'Web Development, Hosting & Digital Services', 'We help businesses grow through innovative web development, reliable hosting, and comprehensive digital solutions.');

-- Insert Default Admin User (password: admin123)
INSERT INTO admin_users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin User', 'super_admin');

-- Insert Default Services
INSERT INTO services (title, description, icon, features, price, price_type) VALUES
('Video Editing', 'Professional, engaging videos that captivate your audience and effectively communicate your message.', 'fas fa-video', '["Professional editing", "Color correction", "Audio enhancement", "Motion graphics"]', 5000.00, 'one-time'),
('App Development', 'Native and cross-platform mobile applications that deliver exceptional user experiences.', 'fas fa-mobile-alt', '["iOS Development", "Android Development", "Cross-platform", "UI/UX Design"]', 50000.00, 'one-time'),
('Graphic Designing & Brand Assets', 'Stunning visual assets that strengthen your brand identity and make a lasting impression.', 'fas fa-paint-brush', '["Logo Design", "Brand Identity", "Marketing Materials", "Social Media Graphics"]', 10000.00, 'one-time'),
('Digital Marketing', 'Strategic marketing solutions to increase your online visibility and drive conversions.', 'fas fa-search', '["SEO Optimization", "Social Media Marketing", "PPC Campaigns", "Content Marketing"]', 15000.00, 'monthly'),
('Web Development', 'Custom websites that are responsive, fast, and designed to convert visitors into customers.', 'fas fa-code', '["Responsive Design", "Custom Development", "CMS Integration", "E-commerce"]', 25000.00, 'one-time'),
('Web Hosting', 'Reliable and affordable hosting solutions with unlimited storage and bandwidth.', 'fas fa-server', '["99.9% Uptime", "24/7 Support", "SSL Certificate", "Daily Backups"]', 75.00, 'monthly');

-- Insert Default Hosting Plan
INSERT INTO hosting_plans (plan_name, price, billing_cycle, features) VALUES
('Standard Server', 75.00, 'monthly', '["Unlimited NVMe SSD Storage", "Unlimited Bandwidth", "Unlimited Email Accounts", "Free SSL Certificate", "WordPress 1-Click Install"]');

-- Insert Default Development Plan
INSERT INTO development_plans (plan_name, building_cost, monthly_cost, features) VALUES
('Simple', 1000.00, 200.00, '["5 Pages Website", "Mobile Responsive Design", "Contact Form"]');

-- Insert Default GPS Product
INSERT INTO gps_products (product_name, description, features, support_features, price) VALUES
('GPS Tracker for Vehicles', 'Our advanced GPS tracking solution for Car, Bike, Scooty, Bus, Truck, Tractor and all types of vehicles.', '["Real Time Tracking", "Location History", "Vehicle lock/unlock", "Smart Notification", "Find Nearby services", "Give 360 degree view of vehicle"]', '["1 year Warranty", "1 year Recharge", "24/7 Assistance"]', 5000.00);

-- Admin Login Attempts Table (for security)
CREATE TABLE admin_login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_address VARCHAR(45) NOT NULL,
    username VARCHAR(255),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert Default Job Positions
INSERT INTO job_positions (title, department, location, job_type, description, requirements, responsibilities) VALUES
('Web Developer', 'Development', 'Remote/On-site', 'full-time', 'Build innovative web solutions for our clients', '["3+ years experience", "PHP, JavaScript, MySQL", "Framework experience", "Problem-solving skills"]', '["Develop web applications", "Code review", "Client communication", "Project planning"]'),
('UI Designer', 'Design', 'Remote/On-site', 'full-time', 'Craft intuitive interfaces that delight users', '["2+ years experience", "Figma, Adobe Creative Suite", "UI/UX principles", "Portfolio required"]', '["Design user interfaces", "Create prototypes", "User research", "Design systems"]');
