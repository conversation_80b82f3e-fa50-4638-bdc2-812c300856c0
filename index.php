<?php
require_once 'config/config.php';

// Get site settings and dynamic content
$settings = getSiteSettings();
$services = fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC, id ASC LIMIT 6");
$hostingPlans = fetchAll("SELECT * FROM hosting_plans WHERE is_active = 1 ORDER BY sort_order ASC, id ASC LIMIT 1");
$developmentPlans = fetchAll("SELECT * FROM development_plans WHERE is_active = 1 ORDER BY sort_order ASC, id ASC LIMIT 1");
$gpsProduct = fetchOne("SELECT * FROM gps_products WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
$jobPositions = fetchAll("SELECT * FROM job_positions WHERE is_active = 1 ORDER BY id ASC LIMIT 2");

// Decode JSON fields
foreach ($services as &$service) {
    $service['features'] = json_decode($service['features'] ?? '[]', true);
}
foreach ($hostingPlans as &$plan) {
    $plan['features'] = json_decode($plan['features'] ?? '[]', true);
}
foreach ($developmentPlans as &$plan) {
    $plan['features'] = json_decode($plan['features'] ?? '[]', true);
}
if ($gpsProduct) {
    $gpsProduct['features'] = json_decode($gpsProduct['features'] ?? '[]', true);
    $gpsProduct['support_features'] = json_decode($gpsProduct['support_features'] ?? '[]', true);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($settings['site_name']); ?> - <?php echo htmlspecialchars($settings['site_description']); ?></title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="container">
            <div class="logo">
                <h1><a href="index.php" style="text-decoration: none; color: inherit;"><?php echo htmlspecialchars($settings['site_name']); ?></a></h1>
            </div>
            <nav>
                <div class="nav-toggle" id="navToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul class="nav-links">
                    <li><a href="index.php" class="active">Home</a></li>
                    <li><a href="pages/services.php">Services</a></li>
                    <li><a href="pages/hosting.php">Hosting</a></li>
                    <li><a href="pages/portfolio.php">Portfolio</a></li>
                    <li><a href="pages/careers.php">Careers</a></li>
                    <li><a href="pages/about.php">About</a></li>
                    <li><a href="pages/contact.php">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content" data-aos="fade-up" data-aos-duration="1000">
                <h1><?php echo htmlspecialchars($settings['hero_title']); ?></h1>
                <h2><?php echo htmlspecialchars($settings['hero_subtitle']); ?></h2>
                <p><?php echo htmlspecialchars($settings['hero_description']); ?></p>
                <div class="hero-buttons">
                    <a href="pages/services.php" class="btn btn-primary">Our Services</a>
                    <a href="pages/contact.php" class="btn btn-secondary">Get a Quote</a>
                </div>
            </div>
            <div class="hero-image" data-aos="fade-left" data-aos-duration="1200">
                <img src="<?php echo !empty($settings['hero_image']) ? htmlspecialchars($settings['hero_image']) : 'https://placehold.co/600x400'; ?>" alt="Digital Services">
            </div>
        </div>
    </section>

    <!-- Services Preview Section -->
    <section class="services-preview">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Our Services</h2>
                <p>Comprehensive solutions to elevate your business</p>
            </div>
            <div class="services-grid">
                <?php
                $delay = 100;
                foreach ($services as $service):
                ?>
                <div class="service-card" data-aos="fade-up" data-aos-delay="<?php echo $delay; ?>">
                    <div class="service-icon">
                        <i class="<?php echo htmlspecialchars($service['icon'] ?: 'fas fa-cog'); ?>"></i>
                    </div>
                    <h3><?php echo htmlspecialchars($service['title']); ?></h3>
                    <p><?php echo htmlspecialchars(truncateText($service['description'], 120)); ?></p>
                    <?php if ($service['price'] > 0): ?>
                        <div class="service-price">
                            <span class="price"><?php echo formatCurrency($service['price']); ?></span>
                            <span class="period"><?php echo $service['price_type'] === 'monthly' ? '/month' : ($service['price_type'] === 'yearly' ? '/year' : ''); ?></span>
                        </div>
                    <?php endif; ?>
                    <a href="pages/services.php#service-<?php echo $service['id']; ?>" class="read-more">Learn More <i class="fas fa-arrow-right"></i></a>
                </div>
                <?php
                $delay += 100;
                endforeach;
                ?>
            </div>
        </div>
    </section>

    <!-- Hosting Plans Section -->
    <section class="hosting-plans">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Hosting Plans</h2>
                <p>Reliable and affordable hosting solutions for your website</p>
            </div>
            <div class="plans-container">
                <?php foreach ($hostingPlans as $plan): ?>
                <div class="plan" data-aos="fade-up" data-aos-delay="100">
                    <div class="plan-header">
                        <h3><?php echo htmlspecialchars($plan['plan_name']); ?></h3>
                        <div class="price">
                            <span class="amount"><?php echo formatCurrency($plan['price']); ?></span>
                            <span class="period">/<?php echo $plan['billing_cycle']; ?></span>
                        </div>
                    </div>
                    <div class="plan-features">
                        <ul>
                            <?php foreach ($plan['features'] as $feature): ?>
                            <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <div class="plan-footer">
                        <a href="pages/contact.php" class="btn btn-primary">Get Started</a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="cta-container" data-aos="fade-up">
                <a href="pages/hosting.php" class="btn btn-secondary">View All Plans</a>
            </div>
        </div>
    </section>

    <!-- Development Plans Preview -->
    <section class="development-preview">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Development Plans</h2>
                <p>Custom website development tailored to your business needs</p>
            </div>
            <div class="plans-container">
                <?php foreach ($developmentPlans as $plan): ?>
                <div class="dev-plan" data-aos="fade-up" data-aos-delay="100">
                    <div class="plan-header">
                        <h3><?php echo htmlspecialchars($plan['plan_name']); ?></h3>
                        <?php if ($plan['building_cost'] > 0): ?>
                        <div class="price">
                            <span class="amount"><?php echo formatCurrency($plan['building_cost']); ?></span>
                            <span class="type">Building Cost</span>
                        </div>
                        <?php endif; ?>
                        <?php if ($plan['monthly_cost'] > 0): ?>
                        <div class="price">
                            <span class="amount"><?php echo formatCurrency($plan['monthly_cost']); ?></span>
                            <span class="period">/month</span>
                            <span class="type">Management</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="plan-features">
                        <ul>
                            <?php foreach ($plan['features'] as $feature): ?>
                            <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <div class="plan-footer">
                        <a href="pages/contact.php" class="btn btn-primary">Choose Plan</a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="cta-container" data-aos="fade-up">
                <a href="pages/services.php#development" class="btn btn-secondary">View All Plans</a>
            </div>
        </div>
    </section>

    <!-- GPS Device Product Section -->
    <?php if ($gpsProduct): ?>
    <section class="gps-device-section">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2><?php echo htmlspecialchars($gpsProduct['product_name']); ?></h2>
                <p>Secure your vehicles with India's Leading GPS Technology</p>
            </div>
            <div class="gps-content-grid">
                <div class="gps-image" data-aos="fade-right">
                    <img src="<?php echo !empty($gpsProduct['image']) ? htmlspecialchars($gpsProduct['image']) : 'https://placehold.co/600x400'; ?>" alt="<?php echo htmlspecialchars($gpsProduct['product_name']); ?>">
                </div>
                <div class="gps-content" data-aos="fade-left">
                    <h3><?php echo htmlspecialchars($gpsProduct['product_name']); ?></h3>
                    <p><?php echo htmlspecialchars($gpsProduct['description']); ?></p>

                    <?php if (!empty($gpsProduct['support_features'])): ?>
                    <div class="gps-feature-section">
                        <h4>Support</h4>
                        <ul class="gps-feature-list">
                            <?php foreach ($gpsProduct['support_features'] as $feature): ?>
                            <li class="gps-feature-item"><i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($gpsProduct['features'])): ?>
                    <div class="gps-feature-section">
                        <h4>Features</h4>
                        <ul class="gps-feature-list">
                            <?php foreach ($gpsProduct['features'] as $feature): ?>
                            <li class="gps-feature-item"><i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <?php if ($gpsProduct['price'] > 0): ?>
                    <div class="gps-price" style="margin: 20px 0;">
                        <span class="price-label">Price: </span>
                        <span class="price-amount"><?php echo formatCurrency($gpsProduct['price']); ?></span>
                    </div>
                    <?php endif; ?>

                    <a href="pages/contact.php" class="btn btn-primary" style="margin-top: 30px;">Get a Quote</a>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Career Opportunities Preview -->
    <?php if (!empty($jobPositions)): ?>
    <section class="careers-preview">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Join Our Team</h2>
                <p>We're looking for exceptional talent to help us build amazing things</p>
            </div>
            <div class="position-cards">
                <?php
                $delay = 100;
                foreach ($jobPositions as $job):
                ?>
                <div class="position-card" data-aos="fade-up" data-aos-delay="<?php echo $delay; ?>">
                    <div class="card-icon">
                        <i class="fas fa-<?php echo $job['department'] === 'Development' ? 'code' : 'pencil-ruler'; ?>"></i>
                    </div>
                    <h3><?php echo htmlspecialchars($job['title']); ?></h3>
                    <p><?php echo htmlspecialchars(truncateText($job['description'], 80)); ?></p>
                    <div class="job-meta">
                        <span class="job-type"><?php echo ucfirst(str_replace('-', ' ', $job['job_type'])); ?></span>
                        <span class="job-location"><?php echo htmlspecialchars($job['location']); ?></span>
                    </div>
                    <a href="pages/careers.php#job-<?php echo $job['id']; ?>" class="btn btn-primary">View Details</a>
                </div>
                <?php
                $delay += 100;
                endforeach;
                ?>
            </div>
            <div class="cta-container" data-aos="fade-up" style="margin-top: 30px;">
                <a href="pages/careers.php" class="btn btn-secondary">View All Openings</a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2><?php echo htmlspecialchars($settings['site_name']); ?></h2>
                    <p><?php echo htmlspecialchars($settings['site_description']); ?></p>
                </div>
                <div class="footer-links">
                    <div class="footer-column">
                        <h3>Services</h3>
                        <ul>
                            <li><a href="pages/services.php#web-development">Web Development</a></li>
                            <li><a href="pages/hosting.php">Web Hosting</a></li>
                            <li><a href="pages/services.php#app-development">App Development</a></li>
                            <li><a href="pages/services.php#digital-marketing">Digital Marketing</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h3>Company</h3>
                        <ul>
                            <li><a href="pages/about.php">About Us</a></li>
                            <li><a href="pages/portfolio.php">Portfolio</a></li>
                            <li><a href="pages/careers.php">Careers</a></li>
                            <li><a href="pages/contact.php">Contact</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h3>Contact</h3>
                        <ul class="contact-info">
                            <li><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($settings['contact_address']); ?></li>
                            <li><i class="fas fa-phone"></i> <?php echo htmlspecialchars($settings['contact_phone']); ?></li>
                            <li><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($settings['contact_email']); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($settings['site_name']); ?>. All rights reserved.</p>
                <div class="social-links">
                    <?php if (!empty($settings['social_facebook'])): ?>
                    <a href="<?php echo htmlspecialchars($settings['social_facebook']); ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                    <?php endif; ?>
                    <?php if (!empty($settings['social_twitter'])): ?>
                    <a href="<?php echo htmlspecialchars($settings['social_twitter']); ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                    <?php endif; ?>
                    <?php if (!empty($settings['social_instagram'])): ?>
                    <a href="<?php echo htmlspecialchars($settings['social_instagram']); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                    <?php endif; ?>
                    <?php if (!empty($settings['social_linkedin'])): ?>
                    <a href="<?php echo htmlspecialchars($settings['social_linkedin']); ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="js/script.js"></script>
</body>
</html>