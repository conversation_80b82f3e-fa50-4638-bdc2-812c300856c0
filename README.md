# DevGlobal - Complete Database-Connected Website

A fully functional business website with admin panel for managing all content dynamically.

## Features

### Frontend Features
- **Dynamic Content**: All content loaded from database
- **Responsive Design**: Works on all devices
- **Contact Forms**: Functional contact form with email notifications
- **Service Showcase**: Dynamic services with pricing
- **Portfolio Display**: Manageable portfolio items
- **Career Listings**: Job positions with application system
- **GPS Product Section**: Configurable product showcase

### Admin Panel Features
- **Dashboard**: Real-time statistics and recent activity
- **Services Management**: Add, edit, delete services
- **Pricing Plans**: Manage hosting and development plans
- **Contact Messages**: View and manage contact form submissions
- **Job Applications**: Handle career applications
- **Portfolio Management**: Add and manage portfolio items
- **Site Settings**: Configure site-wide settings
- **User Management**: Admin user management

### Technical Features
- **PHP Backend**: Secure PHP backend with PDO database connections
- **MySQL Database**: Properly structured database with relationships
- **API Endpoints**: RESTful API for all CRUD operations
- **Security**: SQL injection prevention, input validation, CSRF protection
- **Admin Authentication**: Secure login system with session management
- **File Uploads**: Image and document upload functionality
- **Email Integration**: Contact form email notifications

## Installation

### Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- PDO MySQL extension

### Quick Setup

1. **Upload Files**: Upload all files to your web server
2. **Run Setup**: Visit `http://yoursite.com/setup.php`
3. **Follow Wizard**: Complete the 5-step setup process
4. **Start Managing**: Login to admin panel and start customizing

### Manual Setup

1. **Database Setup**:
   ```sql
   CREATE DATABASE devglobal_db;
   ```
   Import `database/devglobal_database.sql`

2. **Configuration**:
   Edit `config/database.php` with your database credentials

3. **Permissions**:
   ```bash
   chmod 755 uploads/
   chmod 644 config/
   ```

## Default Login Credentials

- **Username**: `admin`
- **Password**: `admin123`
- **Email**: `<EMAIL>`

**⚠️ Important**: Change these credentials immediately after setup!

## File Structure

```
devglobal/
├── config/
│   ├── database.php      # Database configuration
│   └── config.php        # Main configuration
├── api/
│   ├── services.php      # Services API
│   ├── contact.php       # Contact API
│   ├── pricing.php       # Pricing API
│   └── settings.php      # Settings API
├── admin/
│   ├── index.php         # Admin dashboard
│   ├── login.php         # Admin login
│   ├── services.php      # Services management
│   └── css/admin.css     # Admin styles
├── pages/
│   ├── contact.php       # Contact page
│   ├── services.php      # Services page
│   └── ...               # Other pages
├── database/
│   └── devglobal_database.sql  # Database schema
├── uploads/              # File uploads directory
├── css/
│   └── styles.css        # Main stylesheet
├── js/
│   └── script.js         # Main JavaScript
├── index.php             # Homepage
└── setup.php             # Setup wizard
```

## Database Schema

### Main Tables
- `site_settings` - Site configuration
- `services` - Services and their details
- `hosting_plans` - Hosting packages
- `development_plans` - Development packages
- `portfolio_items` - Portfolio projects
- `job_positions` - Career opportunities
- `contact_messages` - Contact form submissions
- `orders` - Service orders
- `job_applications` - Career applications
- `admin_users` - Admin user accounts

## API Endpoints

### Services API (`/api/services.php`)
- `GET` - List all services
- `POST` - Create new service (admin only)
- `PUT` - Update service (admin only)
- `DELETE` - Delete service (admin only)

### Contact API (`/api/contact.php`)
- `GET` - List messages (admin only)
- `POST` - Submit contact form
- `PUT` - Update message status (admin only)
- `DELETE` - Delete message (admin only)

### Settings API (`/api/settings.php`)
- `GET` - Get site settings
- `PUT` - Update settings (admin only)

### Pricing API (`/api/pricing.php`)
- `GET` - List pricing plans
- `POST` - Create plan (admin only)
- `PUT` - Update plan (admin only)
- `DELETE` - Delete plan (admin only)

## Configuration

### Email Settings
Edit `config/config.php`:
```php
define('SMTP_HOST', 'your-smtp-server.com');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-password');
define('FROM_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');
```

### Site URL
Update the site URL in `config/config.php`:
```php
define('SITE_URL', 'https://yourdomain.com');
```

## Security Features

- **SQL Injection Prevention**: All queries use prepared statements
- **Input Validation**: All user inputs are sanitized
- **CSRF Protection**: Forms include CSRF tokens
- **Session Security**: Secure session handling
- **Password Hashing**: Passwords are properly hashed
- **Rate Limiting**: Contact form rate limiting
- **Admin Authentication**: Secure admin login system

## Customization

### Adding New Services
1. Login to admin panel
2. Go to Services section
3. Click "Add Service" button
4. Fill in service details
5. Save and publish

### Modifying Site Settings
1. Login to admin panel
2. Go to Settings section
3. Update site information
4. Save changes

### Managing Content
All content can be managed through the admin panel:
- Services and pricing
- Portfolio items
- Job positions
- Contact information
- Site settings

## Support

For support and customization requests, please contact the development team.

## License

This project is proprietary software. All rights reserved.

---

**DevGlobal** - Professional Web Development & Digital Services Platform
