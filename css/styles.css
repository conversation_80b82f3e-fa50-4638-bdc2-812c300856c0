/* 
   DevGlobal - Master Website Stylesheet
   This file combines styling elements from all websites with a consistent theme
*/

/* ===== Base Variables ===== */
:root {
    /* Primary Color Scheme */
    --primary: #4361ee;
    --secondary: #3a0ca3;
    --accent: #4cc9f0;
    --success: #2ecc71;
    --warning: #f39c12;
    --danger: #e74c3c;
    
    /* Text Colors */
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #ffffff;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #212529;
    
    /* Border & Shadow */
    --border-color: #e9ecef;
    --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 5px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 15px 30px rgba(0, 0, 0, 0.15);
    
    /* Transitions & Animations */
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    
    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --border-radius-round: 50px;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary), var(--secondary));
    --gradient-accent: linear-gradient(135deg, var(--accent), var(--primary));
}

/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

h1 {
    font-size: 3rem;
}

h2 {
    font-size: 2.5rem;
    position: relative;
    display: inline-block;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 3px;
}

h3 {
    font-size: 1.75rem;
}

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    text-decoration: none;
    color: var(--primary);
    transition: var(--transition-fast);
}

a:hover {
    color: var(--secondary);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header p {
    max-width: 600px;
    margin: 0 auto;
}

/* ===== Buttons ===== */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: var(--border-radius-round);
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    border: none;
    font-size: 1rem;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary);
    color: var(--text-light);
}

.btn-primary:hover {
    background-color: var(--secondary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-secondary:hover {
    background-color: var(--primary);
    color: var(--text-light);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* ===== Header ===== */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: var(--bg-primary);
    box-shadow: var(--shadow-sm);
    padding: 15px 0;
    transition: var(--transition-fast);
}

header.scrolled {
    padding: 10px 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.8rem;
    margin-bottom: 0;
    color: var(--primary);
}

nav {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    color: var(--text-primary);
    font-weight: 500;
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-fast);
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 5px;
    cursor: pointer;
}

.nav-toggle span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    transition: var(--transition-fast);
}

/* ===== Hero Section ===== */
.hero {
    padding: 150px 0 80px;
    background-color: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.hero-content h1 {
    margin-bottom: 15px;
}

.hero-content h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--primary);
}

.hero-content h2::after {
    display: none;
}

.hero-content p {
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.hero-buttons {
    display: flex;
    gap: 15px;
}

.hero-image {
    position: relative;
}

.hero-image img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* ===== Services Preview ===== */
.services-preview {
    background-color: var(--bg-primary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    transition: var(--transition-medium);
    box-shadow: var(--shadow-sm);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: var(--text-light);
    font-size: 1.5rem;
}

.service-card h3 {
    margin-bottom: 15px;
}

.service-card p {
    margin-bottom: 20px;
}

.read-more {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

.read-more i {
    transition: var(--transition-fast);
}

.read-more:hover i {
    transform: translateX(5px);
}

/* ===== Hosting Plans ===== */
.hosting-plans {
    background-color: var(--bg-secondary);
}

.plans-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.plan {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-medium);
}

.plan:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.plan-header {
    background-color: var(--primary);
    color: var(--text-light);
    padding: 30px;
    text-align: center;
}

.plan-header h3 {
    color: var(--text-light);
    margin-bottom: 15px;
}

.price {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.price .amount {
    font-size: 2.5rem;
    font-weight: 700;
}

.price .period {
    font-size: 1rem;
}

.price .type {
    font-size: 0.9rem;
    opacity: 0.8;
}

.plan-features {
    padding: 30px;
}

.plan-features ul li {
    padding: 10px 0;
    display: flex;
    align-items: center;
}

.plan-features ul li i {
    color: var(--success);
    margin-right: 10px;
}

.plan-footer {
    padding: 0 30px 30px;
    text-align: center;
}

/* ===== Development Plans ===== */
.development-preview {
    background-color: var(--bg-primary);
}

.dev-plan {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-medium);
    position: relative;
}

.dev-plan:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.dev-plan.featured {
    transform: scale(1.05);
    border: 2px solid var(--primary);
    z-index: 1;
}

.dev-plan.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.popular-tag {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--accent);
    color: white;
    padding: 5px 15px;
    font-size: 0.8rem;
    border-bottom-left-radius: 10px;
}

.cta-container {
    text-align: center;
    margin-top: 40px;
}

/* ===== Careers Preview ===== */
.careers-preview {
    background-color: var(--bg-secondary);
}

.position-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.position-card {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-medium);
}

.position-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--text-light);
    font-size: 1.8rem;
}

.position-card h3 {
    margin-bottom: 15px;
}

.position-card p {
    margin-bottom: 25px;
}

/* ===== Footer ===== */
footer {
    background-color: var(--bg-dark);
    color: var(--text-light);
    padding: 80px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 50px;
    margin-bottom: 50px;
}

.footer-logo h2 {
    color: var(--text-light);
    margin-bottom: 10px;
}

.footer-logo h2::after {
    display: none;
}

.footer-logo p {
    color: var(--text-light);
    opacity: 0.7;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.footer-column h3 {
    color: var(--text-light);
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: var(--text-light);
    opacity: 0.7;
    transition: var(--transition-fast);
}

.footer-column ul li a:hover {
    opacity: 1;
    color: var(--accent);
}

.contact-info li {
    display: flex;
    align-items: center;
    gap: 10px;
    opacity: 0.7;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: var(--text-light);
    opacity: 0.7;
    margin-bottom: 0;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    transition: var(--transition-fast);
}

.social-links a:hover {
    background-color: var(--primary);
    transform: translateY(-3px);
}

/* ===== Testimonials Section ===== */
.testimonials {
    background-color: var(--bg-secondary);
    padding: 100px 0;
}

.testimonials-slider {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-md);
    transition: var(--transition-medium);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.testimonial-content {
    flex: 1;
    margin-bottom: 20px;
    position: relative;
}

.testimonial-content p {
    font-style: italic;
    color: var(--text-secondary);
    line-height: 1.8;
    position: relative;
    padding-left: 25px;
}

.testimonial-content p::before {
    content: '\201C';
    font-size: 60px;
    color: var(--primary);
    opacity: 0.3;
    position: absolute;
    top: -20px;
    left: -5px;
    font-family: serif;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary);
}

.author-info h4 {
    margin-bottom: 5px;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.author-info p {
    margin-bottom: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* ===== GPS Device Section ===== */
.gps-device-section {
    padding: 100px 0;
    background-color: var(--bg-secondary);
}

.gps-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.gps-image img {
    width: 100%;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.gps-content h3 {
    margin-bottom: 20px;
}

.gps-content p {
    margin-bottom: 30px;
}

.gps-feature-section {
    margin-bottom: 30px;
}

.gps-feature-section h4 {
    margin-bottom: 15px;
    color: var(--primary);
}

.gps-feature-list {
    list-style: none;
    padding: 0;
}

.gps-feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.gps-feature-item i {
    color: var(--primary);
    margin-right: 10px;
}

/* ===== Responsive Styles ===== */
@media (max-width: 992px) {
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .hero .container {
        grid-template-columns: 1fr;
    }
    
    .hero-image {
        order: -1;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
    }
    
    .gps-content-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .nav-links {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: var(--bg-primary);
        flex-direction: column;
        gap: 0;
        box-shadow: var(--shadow-md);
        padding: 20px 0;
        transform: translateY(-150%);
        transition: var(--transition-medium);
        z-index: 999;
    }
    
    .nav-links.active {
        transform: translateY(0);
    }
    
    .nav-links li {
        width: 100%;
        text-align: center;
    }
    
    .nav-links a {
        display: block;
        padding: 15px 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    section {
        padding: 60px 0;
    }
    
    .hero {
        padding: 120px 0 60px;
    }
    
    .hero-buttons {
        flex-direction: column;
    }
    
    .services-grid,
    .plans-container,
    .position-cards {
        grid-template-columns: 1fr;
    }
    
    .gps-device-section {
        padding: 60px 0;
    }
    
    .gps-image {
        margin-bottom: 20px;
    }
    
    .gps-content h3 {
        font-size: 1.5rem;
    }
}