<?php
/**
 * Settings API Endpoint
 * Handles site settings management
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Handle CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'PUT':
            handlePut($input);
            break;
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
}

function handleGet() {
    // Get site settings (public endpoint for basic info)
    $settings = getSiteSettings();
    
    if (isAdminLoggedIn()) {
        // Return all settings for admin
        jsonResponse(['success' => true, 'data' => $settings]);
    } else {
        // Return only public settings for non-admin
        $publicSettings = [
            'site_name' => $settings['site_name'],
            'site_description' => $settings['site_description'],
            'contact_email' => $settings['contact_email'],
            'contact_phone' => $settings['contact_phone'],
            'contact_address' => $settings['contact_address'],
            'social_facebook' => $settings['social_facebook'] ?? '',
            'social_twitter' => $settings['social_twitter'] ?? '',
            'social_instagram' => $settings['social_instagram'] ?? '',
            'social_linkedin' => $settings['social_linkedin'] ?? '',
            'hero_title' => $settings['hero_title'],
            'hero_subtitle' => $settings['hero_subtitle'],
            'hero_description' => $settings['hero_description']
        ];
        jsonResponse(['success' => true, 'data' => $publicSettings]);
    }
}

function handlePut($input) {
    // Admin only for updating settings
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    // Get current settings
    $currentSettings = fetchOne("SELECT * FROM site_settings WHERE id = 1");
    
    // Prepare data for update
    $data = [];
    
    // Site information
    if (isset($input['site_name'])) {
        $data['site_name'] = sanitizeInput($input['site_name']);
    }
    if (isset($input['site_description'])) {
        $data['site_description'] = sanitizeInput($input['site_description']);
    }
    if (isset($input['site_logo'])) {
        $data['site_logo'] = sanitizeInput($input['site_logo']);
    }
    
    // Contact information
    if (isset($input['contact_email'])) {
        if (!isValidEmail($input['contact_email'])) {
            jsonResponse(['error' => 'Invalid contact email address'], 400);
        }
        $data['contact_email'] = sanitizeInput($input['contact_email']);
    }
    if (isset($input['contact_phone'])) {
        $data['contact_phone'] = sanitizeInput($input['contact_phone']);
    }
    if (isset($input['contact_address'])) {
        $data['contact_address'] = sanitizeInput($input['contact_address']);
    }
    
    // Social media links
    if (isset($input['social_facebook'])) {
        $data['social_facebook'] = sanitizeInput($input['social_facebook']);
    }
    if (isset($input['social_twitter'])) {
        $data['social_twitter'] = sanitizeInput($input['social_twitter']);
    }
    if (isset($input['social_instagram'])) {
        $data['social_instagram'] = sanitizeInput($input['social_instagram']);
    }
    if (isset($input['social_linkedin'])) {
        $data['social_linkedin'] = sanitizeInput($input['social_linkedin']);
    }
    
    // Hero section
    if (isset($input['hero_title'])) {
        $data['hero_title'] = sanitizeInput($input['hero_title']);
    }
    if (isset($input['hero_subtitle'])) {
        $data['hero_subtitle'] = sanitizeInput($input['hero_subtitle']);
    }
    if (isset($input['hero_description'])) {
        $data['hero_description'] = sanitizeInput($input['hero_description']);
    }
    if (isset($input['hero_image'])) {
        $data['hero_image'] = sanitizeInput($input['hero_image']);
    }
    
    if (empty($data)) {
        jsonResponse(['error' => 'No data provided for update'], 400);
    }
    
    $success = false;
    
    if ($currentSettings) {
        // Update existing settings
        $success = updateData('site_settings', $data, 'id = 1');
    } else {
        // Insert new settings record
        $data['id'] = 1;
        $success = insertData('site_settings', $data);
    }
    
    if ($success) {
        logAdminActivity('Updated site settings', 'Settings updated: ' . implode(', ', array_keys($data)));
        
        // Clear any cached settings
        // If you implement caching, clear it here
        
        jsonResponse(['success' => true, 'message' => 'Settings updated successfully']);
    } else {
        jsonResponse(['error' => 'Failed to update settings'], 500);
    }
}

// Additional endpoint for specific setting categories
if (isset($_GET['category'])) {
    handleCategoryRequest($_GET['category']);
}

function handleCategoryRequest($category) {
    switch ($category) {
        case 'stats':
            handleStatsRequest();
            break;
        case 'dashboard':
            handleDashboardRequest();
            break;
        default:
            jsonResponse(['error' => 'Invalid category'], 400);
    }
}

function handleStatsRequest() {
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    // Get dashboard statistics
    $stats = [];
    
    // New orders count
    $newOrders = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending' AND created_at > DATE_SUB(NOW(), INTERVAL 1 WEEK)");
    $stats['new_orders'] = $newOrders['count'] ?? 0;
    
    // Job applications count
    $jobApplications = fetchOne("SELECT COUNT(*) as count FROM job_applications WHERE status = 'new' AND created_at > DATE_SUB(NOW(), INTERVAL 1 WEEK)");
    $stats['job_applications'] = $jobApplications['count'] ?? 0;
    
    // Revenue calculation (this month)
    $revenue = fetchOne("SELECT SUM(total_amount) as total FROM orders WHERE payment_status = 'paid' AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')");
    $stats['revenue'] = $revenue['total'] ?? 0;
    
    // Active projects count
    $activeProjects = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status IN ('confirmed', 'in-progress')");
    $stats['active_projects'] = $activeProjects['count'] ?? 0;
    
    // Contact messages count
    $contactMessages = fetchOne("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'");
    $stats['contact_messages'] = $contactMessages['count'] ?? 0;
    
    jsonResponse(['success' => true, 'data' => $stats]);
}

function handleDashboardRequest() {
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    // Get recent activity data
    $recentOrders = fetchAll("SELECT * FROM orders ORDER BY created_at DESC LIMIT 5");
    $recentMessages = fetchAll("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5");
    $recentApplications = fetchAll("SELECT ja.*, jp.title as job_title FROM job_applications ja JOIN job_positions jp ON ja.job_id = jp.id ORDER BY ja.created_at DESC LIMIT 5");
    
    $dashboardData = [
        'recent_orders' => $recentOrders,
        'recent_messages' => $recentMessages,
        'recent_applications' => $recentApplications
    ];
    
    jsonResponse(['success' => true, 'data' => $dashboardData]);
}
?>
