<?php
/**
 * Contact API Endpoint
 * Handles contact form submissions and management
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Handle CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost($input);
            break;
        case 'PUT':
            handlePut($input);
            break;
        case 'DELETE':
            handleDelete();
            break;
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
}

function handleGet() {
    // Admin only for viewing messages
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    $id = $_GET['id'] ?? null;
    
    if ($id) {
        // Get single message
        $message = fetchOne("SELECT * FROM contact_messages WHERE id = :id", ['id' => $id]);
        if ($message) {
            jsonResponse(['success' => true, 'data' => $message]);
        } else {
            jsonResponse(['error' => 'Message not found'], 404);
        }
    } else {
        // Get all messages with pagination
        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? ADMIN_ITEMS_PER_PAGE);
        $status = $_GET['status'] ?? null;
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT * FROM contact_messages";
        $countSql = "SELECT COUNT(*) as total FROM contact_messages";
        $params = [];
        
        if ($status && in_array($status, ['new', 'read', 'replied', 'archived'])) {
            $sql .= " WHERE status = :status";
            $countSql .= " WHERE status = :status";
            $params['status'] = $status;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
        
        // Get total count
        $totalResult = fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // Add pagination parameters
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $messages = fetchAll($sql, $params);
        
        jsonResponse([
            'success' => true,
            'data' => $messages,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    }
}

function handlePost($input) {
    // Handle contact form submission (public endpoint)
    
    // Basic rate limiting by IP
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $recentMessages = fetchOne(
        "SELECT COUNT(*) as count FROM contact_messages WHERE ip_address = :ip AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
        ['ip' => $ip]
    );
    
    if ($recentMessages && $recentMessages['count'] >= 5) {
        jsonResponse(['error' => 'Too many messages sent. Please try again later.'], 429);
    }
    
    // Validate required fields
    $required = ['name', 'email', 'message'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            jsonResponse(['error' => "Field '$field' is required"], 400);
        }
    }
    
    // Validate email
    if (!isValidEmail($input['email'])) {
        jsonResponse(['error' => 'Invalid email address'], 400);
    }
    
    // Prepare data for insertion
    $data = [
        'name' => sanitizeInput($input['name']),
        'email' => sanitizeInput($input['email']),
        'phone' => sanitizeInput($input['phone'] ?? ''),
        'subject' => sanitizeInput($input['subject'] ?? 'Website Contact'),
        'message' => sanitizeInput($input['message']),
        'ip_address' => $ip,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];
    
    $messageId = insertData('contact_messages', $data);
    
    if ($messageId) {
        // Send email notification to admin
        $emailSubject = 'New Contact Message - ' . $data['subject'];
        $emailBody = "
        <h2>New Contact Message</h2>
        <p><strong>Name:</strong> {$data['name']}</p>
        <p><strong>Email:</strong> {$data['email']}</p>
        <p><strong>Phone:</strong> {$data['phone']}</p>
        <p><strong>Subject:</strong> {$data['subject']}</p>
        <p><strong>Message:</strong></p>
        <p>{$data['message']}</p>
        <p><strong>Submitted:</strong> " . date('Y-m-d H:i:s') . "</p>
        ";
        
        sendEmail(ADMIN_EMAIL, $emailSubject, $emailBody, true);
        
        jsonResponse([
            'success' => true, 
            'message' => 'Thank you for your message! We will get back to you soon.',
            'id' => $messageId
        ]);
    } else {
        jsonResponse(['error' => 'Failed to send message. Please try again.'], 500);
    }
}

function handlePut($input) {
    // Admin only for updating message status
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['error' => 'Message ID is required'], 400);
    }
    
    // Check if message exists
    $existingMessage = fetchOne("SELECT * FROM contact_messages WHERE id = :id", ['id' => $id]);
    if (!$existingMessage) {
        jsonResponse(['error' => 'Message not found'], 404);
    }
    
    // Prepare data for update
    $data = [];
    
    if (isset($input['status']) && in_array($input['status'], ['new', 'read', 'replied', 'archived'])) {
        $data['status'] = $input['status'];
    }
    
    if (empty($data)) {
        jsonResponse(['error' => 'No valid data provided for update'], 400);
    }
    
    $success = updateData('contact_messages', $data, 'id = :id', ['id' => $id]);
    
    if ($success) {
        logAdminActivity('Updated contact message', "Message ID: $id, Status: " . ($data['status'] ?? 'unchanged'));
        jsonResponse(['success' => true, 'message' => 'Message updated successfully']);
    } else {
        jsonResponse(['error' => 'Failed to update message'], 500);
    }
}

function handleDelete() {
    // Admin only for deleting messages
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['error' => 'Message ID is required'], 400);
    }
    
    // Check if message exists
    $existingMessage = fetchOne("SELECT * FROM contact_messages WHERE id = :id", ['id' => $id]);
    if (!$existingMessage) {
        jsonResponse(['error' => 'Message not found'], 404);
    }
    
    $success = deleteData('contact_messages', 'id = :id', ['id' => $id]);
    
    if ($success) {
        logAdminActivity('Deleted contact message', "Message ID: $id, From: " . $existingMessage['name']);
        jsonResponse(['success' => true, 'message' => 'Message deleted successfully']);
    } else {
        jsonResponse(['error' => 'Failed to delete message'], 500);
    }
}
?>
