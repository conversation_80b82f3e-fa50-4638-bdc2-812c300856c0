<?php
/**
 * Pricing API Endpoint
 * Handles CRUD operations for hosting and development plans
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Handle CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);
$type = $_GET['type'] ?? 'hosting'; // hosting or development

try {
    switch ($method) {
        case 'GET':
            handleGet($type);
            break;
        case 'POST':
            handlePost($input, $type);
            break;
        case 'PUT':
            handlePut($input, $type);
            break;
        case 'DELETE':
            handleDelete($type);
            break;
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
}

function getTableName($type) {
    return $type === 'development' ? 'development_plans' : 'hosting_plans';
}

function handleGet($type) {
    $table = getTableName($type);
    $id = $_GET['id'] ?? null;
    
    if ($id) {
        // Get single plan
        $plan = fetchOne("SELECT * FROM {$table} WHERE id = :id", ['id' => $id]);
        if ($plan) {
            $plan['features'] = json_decode($plan['features'] ?? '[]', true);
            jsonResponse(['success' => true, 'data' => $plan]);
        } else {
            jsonResponse(['error' => 'Plan not found'], 404);
        }
    } else {
        // Get all plans
        $activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === 'true';
        $sql = "SELECT * FROM {$table}";
        $params = [];
        
        if ($activeOnly) {
            $sql .= " WHERE is_active = 1";
        }
        
        $sql .= " ORDER BY sort_order ASC, id ASC";
        
        $plans = fetchAll($sql, $params);
        
        // Decode JSON fields for each plan
        foreach ($plans as &$plan) {
            $plan['features'] = json_decode($plan['features'] ?? '[]', true);
        }
        
        jsonResponse(['success' => true, 'data' => $plans]);
    }
}

function handlePost($input, $type) {
    // Check admin authentication
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    $table = getTableName($type);
    
    // Validate required fields based on type
    if ($type === 'development') {
        $required = ['plan_name'];
        $data = [
            'plan_name' => sanitizeInput($input['plan_name']),
            'building_cost' => floatval($input['building_cost'] ?? 0),
            'monthly_cost' => floatval($input['monthly_cost'] ?? 0),
            'features' => json_encode($input['features'] ?? []),
            'is_popular' => isset($input['is_popular']) ? (bool)$input['is_popular'] : false,
            'is_active' => isset($input['is_active']) ? (bool)$input['is_active'] : true,
            'sort_order' => intval($input['sort_order'] ?? 0)
        ];
    } else {
        $required = ['plan_name', 'price'];
        $data = [
            'plan_name' => sanitizeInput($input['plan_name']),
            'price' => floatval($input['price']),
            'billing_cycle' => in_array($input['billing_cycle'] ?? '', ['monthly', 'yearly']) ? $input['billing_cycle'] : 'monthly',
            'features' => json_encode($input['features'] ?? []),
            'is_popular' => isset($input['is_popular']) ? (bool)$input['is_popular'] : false,
            'is_active' => isset($input['is_active']) ? (bool)$input['is_active'] : true,
            'sort_order' => intval($input['sort_order'] ?? 0)
        ];
    }
    
    // Validate required fields
    foreach ($required as $field) {
        if (empty($input[$field])) {
            jsonResponse(['error' => "Field '$field' is required"], 400);
        }
    }
    
    $planId = insertData($table, $data);
    
    if ($planId) {
        logAdminActivity("Created {$type} plan", "Plan ID: $planId, Name: " . $data['plan_name']);
        jsonResponse(['success' => true, 'message' => ucfirst($type) . ' plan created successfully', 'id' => $planId]);
    } else {
        jsonResponse(['error' => 'Failed to create plan'], 500);
    }
}

function handlePut($input, $type) {
    // Check admin authentication
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    $table = getTableName($type);
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        jsonResponse(['error' => 'Plan ID is required'], 400);
    }
    
    // Check if plan exists
    $existingPlan = fetchOne("SELECT * FROM {$table} WHERE id = :id", ['id' => $id]);
    if (!$existingPlan) {
        jsonResponse(['error' => 'Plan not found'], 404);
    }
    
    // Prepare data for update
    $data = [];
    
    if (isset($input['plan_name'])) {
        $data['plan_name'] = sanitizeInput($input['plan_name']);
    }
    
    if ($type === 'development') {
        if (isset($input['building_cost'])) {
            $data['building_cost'] = floatval($input['building_cost']);
        }
        if (isset($input['monthly_cost'])) {
            $data['monthly_cost'] = floatval($input['monthly_cost']);
        }
    } else {
        if (isset($input['price'])) {
            $data['price'] = floatval($input['price']);
        }
        if (isset($input['billing_cycle']) && in_array($input['billing_cycle'], ['monthly', 'yearly'])) {
            $data['billing_cycle'] = $input['billing_cycle'];
        }
    }
    
    if (isset($input['features'])) {
        $data['features'] = json_encode($input['features']);
    }
    if (isset($input['is_popular'])) {
        $data['is_popular'] = (bool)$input['is_popular'];
    }
    if (isset($input['is_active'])) {
        $data['is_active'] = (bool)$input['is_active'];
    }
    if (isset($input['sort_order'])) {
        $data['sort_order'] = intval($input['sort_order']);
    }
    
    if (empty($data)) {
        jsonResponse(['error' => 'No data provided for update'], 400);
    }
    
    $success = updateData($table, $data, 'id = :id', ['id' => $id]);
    
    if ($success) {
        logAdminActivity("Updated {$type} plan", "Plan ID: $id");
        jsonResponse(['success' => true, 'message' => ucfirst($type) . ' plan updated successfully']);
    } else {
        jsonResponse(['error' => 'Failed to update plan'], 500);
    }
}

function handleDelete($type) {
    // Check admin authentication
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    $table = getTableName($type);
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        jsonResponse(['error' => 'Plan ID is required'], 400);
    }
    
    // Check if plan exists
    $existingPlan = fetchOne("SELECT * FROM {$table} WHERE id = :id", ['id' => $id]);
    if (!$existingPlan) {
        jsonResponse(['error' => 'Plan not found'], 404);
    }
    
    $success = deleteData($table, 'id = :id', ['id' => $id]);
    
    if ($success) {
        logAdminActivity("Deleted {$type} plan", "Plan ID: $id, Name: " . $existingPlan['plan_name']);
        jsonResponse(['success' => true, 'message' => ucfirst($type) . ' plan deleted successfully']);
    } else {
        jsonResponse(['error' => 'Failed to delete plan'], 500);
    }
}
?>
