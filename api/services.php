<?php
/**
 * Services API Endpoint
 * Handles CRUD operations for services
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Handle CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost($input);
            break;
        case 'PUT':
            handlePut($input);
            break;
        case 'DELETE':
            handleDelete();
            break;
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
}

function handleGet() {
    $id = $_GET['id'] ?? null;
    
    if ($id) {
        // Get single service
        $service = fetchOne("SELECT * FROM services WHERE id = :id", ['id' => $id]);
        if ($service) {
            // Decode JSON fields
            $service['features'] = json_decode($service['features'] ?? '[]', true);
            jsonResponse(['success' => true, 'data' => $service]);
        } else {
            jsonResponse(['error' => 'Service not found'], 404);
        }
    } else {
        // Get all services
        $activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === 'true';
        $sql = "SELECT * FROM services";
        $params = [];
        
        if ($activeOnly) {
            $sql .= " WHERE is_active = 1";
        }
        
        $sql .= " ORDER BY sort_order ASC, id ASC";
        
        $services = fetchAll($sql, $params);
        
        // Decode JSON fields for each service
        foreach ($services as &$service) {
            $service['features'] = json_decode($service['features'] ?? '[]', true);
        }
        
        jsonResponse(['success' => true, 'data' => $services]);
    }
}

function handlePost($input) {
    // Check admin authentication for write operations
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    // Validate required fields
    $required = ['title', 'description'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            jsonResponse(['error' => "Field '$field' is required"], 400);
        }
    }
    
    // Prepare data for insertion
    $data = [
        'title' => sanitizeInput($input['title']),
        'description' => sanitizeInput($input['description']),
        'icon' => sanitizeInput($input['icon'] ?? ''),
        'image' => sanitizeInput($input['image'] ?? ''),
        'features' => json_encode($input['features'] ?? []),
        'price' => floatval($input['price'] ?? 0),
        'price_type' => in_array($input['price_type'] ?? '', ['one-time', 'monthly', 'yearly']) ? $input['price_type'] : 'one-time',
        'is_active' => isset($input['is_active']) ? (bool)$input['is_active'] : true,
        'sort_order' => intval($input['sort_order'] ?? 0)
    ];
    
    $serviceId = insertData('services', $data);
    
    if ($serviceId) {
        logAdminActivity('Created service', "Service ID: $serviceId, Title: " . $data['title']);
        jsonResponse(['success' => true, 'message' => 'Service created successfully', 'id' => $serviceId]);
    } else {
        jsonResponse(['error' => 'Failed to create service'], 500);
    }
}

function handlePut($input) {
    // Check admin authentication
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['error' => 'Service ID is required'], 400);
    }
    
    // Check if service exists
    $existingService = fetchOne("SELECT * FROM services WHERE id = :id", ['id' => $id]);
    if (!$existingService) {
        jsonResponse(['error' => 'Service not found'], 404);
    }
    
    // Prepare data for update
    $data = [];
    
    if (isset($input['title'])) {
        $data['title'] = sanitizeInput($input['title']);
    }
    if (isset($input['description'])) {
        $data['description'] = sanitizeInput($input['description']);
    }
    if (isset($input['icon'])) {
        $data['icon'] = sanitizeInput($input['icon']);
    }
    if (isset($input['image'])) {
        $data['image'] = sanitizeInput($input['image']);
    }
    if (isset($input['features'])) {
        $data['features'] = json_encode($input['features']);
    }
    if (isset($input['price'])) {
        $data['price'] = floatval($input['price']);
    }
    if (isset($input['price_type']) && in_array($input['price_type'], ['one-time', 'monthly', 'yearly'])) {
        $data['price_type'] = $input['price_type'];
    }
    if (isset($input['is_active'])) {
        $data['is_active'] = (bool)$input['is_active'];
    }
    if (isset($input['sort_order'])) {
        $data['sort_order'] = intval($input['sort_order']);
    }
    
    if (empty($data)) {
        jsonResponse(['error' => 'No data provided for update'], 400);
    }
    
    $success = updateData('services', $data, 'id = :id', ['id' => $id]);
    
    if ($success) {
        logAdminActivity('Updated service', "Service ID: $id");
        jsonResponse(['success' => true, 'message' => 'Service updated successfully']);
    } else {
        jsonResponse(['error' => 'Failed to update service'], 500);
    }
}

function handleDelete() {
    // Check admin authentication
    if (!isAdminLoggedIn()) {
        jsonResponse(['error' => 'Unauthorized'], 401);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['error' => 'Service ID is required'], 400);
    }
    
    // Check if service exists
    $existingService = fetchOne("SELECT * FROM services WHERE id = :id", ['id' => $id]);
    if (!$existingService) {
        jsonResponse(['error' => 'Service not found'], 404);
    }
    
    $success = deleteData('services', 'id = :id', ['id' => $id]);
    
    if ($success) {
        logAdminActivity('Deleted service', "Service ID: $id, Title: " . $existingService['title']);
        jsonResponse(['success' => true, 'message' => 'Service deleted successfully']);
    } else {
        jsonResponse(['error' => 'Failed to delete service'], 500);
    }
}
?>
