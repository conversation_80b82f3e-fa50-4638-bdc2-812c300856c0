<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Management - DevGlobal Admin</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h1>DevGlobal</h1>
                <p>Admin Panel</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="services.html">
                            <i class="fas fa-cogs"></i>
                            <span>Services</span>
                        </a>
                    </li>
                    <li>
                        <a href="pricing.html">
                            <i class="fas fa-tags"></i>
                            <span>Pricing Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="orders.html">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li>
                        <a href="hiring.html">
                            <i class="fas fa-users"></i>
                            <span>Hiring</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="portfolio.html">
                            <i class="fas fa-briefcase"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Top Header -->
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search portfolio items...">
                </div>
                <div class="header-actions">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="admin-profile">
                        <img src="https://placehold.co/100x100" alt="Admin">
                        <span>Admin User</span>
                    </div>
                </div>
            </header>

            <!-- Portfolio Content -->
            <div class="dashboard-content">
                <div class="page-header">
                    <h2>Portfolio Management</h2>
                    <p>Manage your portfolio projects and case studies</p>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons" style="margin-bottom: 25px;">
                    <button class="btn btn-primary" id="addPortfolioBtn">
                        <i class="fas fa-plus"></i> Add New Project
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-file-export"></i> Export Portfolio
                    </button>
                </div>

                <!-- Portfolio Categories Tabs -->
                <div class="tabs-container" style="margin-bottom: 25px;">
                    <div class="tabs">
                        <button class="tab active" data-category="all">All Projects</button>
                        <button class="tab" data-category="web">Web Development</button>
                        <button class="tab" data-category="app">App Development</button>
                        <button class="tab" data-category="design">Graphic Design</button>
                        <button class="tab" data-category="marketing">Digital Marketing</button>
                    </div>
                </div>

                <!-- Portfolio Grid View -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h3>Portfolio Projects</h3>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid"><i class="fas fa-th"></i></button>
                            <button class="view-btn" data-view="list"><i class="fas fa-list"></i></button>
                        </div>
                    </div>
                    
                    <div class="portfolio-grid active-view" id="grid-view">
                        <!-- Portfolio Item 1 -->
                        <div class="portfolio-card">
                            <div class="portfolio-image">
                                <img src="https://placehold.co/600x400" alt="E-commerce Website">
                                <div class="portfolio-actions">
                                    <button class="btn-icon edit-portfolio" data-id="PORT-001">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon delete-portfolio" data-id="PORT-001">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="portfolio-content">
                                <h4>E-commerce Website</h4>
                                <p class="portfolio-category">Web Development</p>
                                <p class="portfolio-desc">A fully responsive e-commerce platform with payment integration.</p>
                                <div class="portfolio-meta">
                                    <span><i class="fas fa-calendar"></i> June 2023</span>
                                    <span class="status-badge status-completed">Completed</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Portfolio Item 2 -->
                        <div class="portfolio-card">
                            <div class="portfolio-image">
                                <img src="https://placehold.co/600x400" alt="Food Delivery App">
                                <div class="portfolio-actions">
                                    <button class="btn-icon edit-portfolio" data-id="PORT-002">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon delete-portfolio" data-id="PORT-002">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="portfolio-content">
                                <h4>Food Delivery App</h4>
                                <p class="portfolio-category">App Development</p>
                                <p class="portfolio-desc">A mobile app for food ordering and delivery tracking.</p>
                                <div class="portfolio-meta">
                                    <span><i class="fas fa-calendar"></i> May 2023</span>
                                    <span class="status-badge status-completed">Completed</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Portfolio Item 3 -->
                        <div class="portfolio-card">
                            <div class="portfolio-image">
                                <img src="https://placehold.co/600x400" alt="Brand Identity Design">
                                <div class="portfolio-actions">
                                    <button class="btn-icon edit-portfolio" data-id="PORT-003">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon delete-portfolio" data-id="PORT-003">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="portfolio-content">
                                <h4>Brand Identity Design</h4>
                                <p class="portfolio-category">Graphic Design</p>
                                <p class="portfolio-desc">Complete brand identity including logo, color palette, and brand guidelines.</p>
                                <div class="portfolio-meta">
                                    <span><i class="fas fa-calendar"></i> April 2023</span>
                                    <span class="status-badge status-completed">Completed</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Portfolio Item 4 -->
                        <div class="portfolio-card">
                            <div class="portfolio-image">
                                <img src="https://placehold.co/600x400" alt="SEO Campaign">
                                <div class="portfolio-actions">
                                    <button class="btn-icon edit-portfolio" data-id="PORT-004">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon delete-portfolio" data-id="PORT-004">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="portfolio-content">
                                <h4>SEO Campaign</h4>
                                <p class="portfolio-category">Digital Marketing</p>
                                <p class="portfolio-desc">Comprehensive SEO strategy that increased organic traffic by 150%.</p>
                                <div class="portfolio-meta">
                                    <span><i class="fas fa-calendar"></i> March 2023</span>
                                    <span class="status-badge status-completed">Completed</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Portfolio Item 5 -->
                        <div class="portfolio-card">
                            <div class="portfolio-image">
                                <img src="https://placehold.co/600x400" alt="Real Estate Website">
                                <div class="portfolio-actions">
                                    <button class="btn-icon edit-portfolio" data-id="PORT-005">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon delete-portfolio" data-id="PORT-005">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="portfolio-content">
                                <h4>Real Estate Website</h4>
                                <p class="portfolio-category">Web Development</p>
                                <p class="portfolio-desc">Property listing website with advanced search and filtering options.</p>
                                <div class="portfolio-meta">
                                    <span><i class="fas fa-calendar"></i> February 2023</span>
                                    <span class="status-badge status-progress">In Progress</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Portfolio Item 6 -->
                        <div class="portfolio-card">
                            <div class="portfolio-image">
                                <img src="https://placehold.co/600x400" alt="Fitness Tracking App">
                                <div class="portfolio-actions">
                                    <button class="btn-icon edit-portfolio" data-id="PORT-006">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon delete-portfolio" data-id="PORT-006">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="portfolio-content">
                                <h4>Fitness Tracking App</h4>
                                <p class="portfolio-category">App Development</p>
                                <p class="portfolio-desc">Mobile app for tracking workouts, nutrition, and fitness goals.</p>
                                <div class="portfolio-meta">
                                    <span><i class="fas fa-calendar"></i> January 2023</span>
                                    <span class="status-badge status-progress">In Progress</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Portfolio List View -->
                    <div class="portfolio-list" id="list-view">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Project Name</th>
                                    <th>Category</th>
                                    <th>Client</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>PORT-001</td>
                                    <td>E-commerce Website</td>
                                    <td>Web Development</td>
                                    <td>Fashion Boutique</td>
                                    <td>June 2023</td>
                                    <td><span class="status-badge status-completed">Completed</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-portfolio" data-id="PORT-001">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-portfolio" data-id="PORT-001">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-portfolio" data-id="PORT-001">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>PORT-002</td>
                                    <td>Food Delivery App</td>
                                    <td>App Development</td>
                                    <td>Local Restaurant</td>
                                    <td>May 2023</td>
                                    <td><span class="status-badge status-completed">Completed</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-portfolio" data-id="PORT-002">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-portfolio" data-id="PORT-002">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-portfolio" data-id="PORT-002">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>PORT-003</td>
                                    <td>Brand Identity Design</td>
                                    <td>Graphic Design</td>
                                    <td>Startup Company</td>
                                    <td>April 2023</td>
                                    <td><span class="status-badge status-completed">Completed</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-portfolio" data-id="PORT-003">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-portfolio" data-id="PORT-003">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-portfolio" data-id="PORT-003">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>PORT-004</td>
                                    <td>SEO Campaign</td>
                                    <td>Digital Marketing</td>
                                    <td>Online Store</td>
                                    <td>March 2023</td>
                                    <td><span class="status-badge status-completed">Completed</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-portfolio" data-id="PORT-004">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-portfolio" data-id="PORT-004">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-portfolio" data-id="PORT-004">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>PORT-005</td>
                                    <td>Real Estate Website</td>
                                    <td>Web Development</td>
                                    <td>Property Agency</td>
                                    <td>February 2023</td>
                                    <td><span class="status-badge status-progress">In Progress</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-portfolio" data-id="PORT-005">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-portfolio" data-id="PORT-005">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-portfolio" data-id="PORT-005">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>PORT-006</td>
                                    <td>Fitness Tracking App</td>
                                    <td>App Development</td>
                                    <td>Fitness Center</td>
                                    <td>January 2023</td>
                                    <td><span class="status-badge status-progress">In Progress</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-portfolio" data-id="PORT-006">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-portfolio" data-id="PORT-006">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-portfolio" data-id="PORT-006">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="pagination">
                        <button class="page-btn"><i class="fas fa-chevron-left"></i></button>
                        <button class="page-btn active">1</button>
                        <button class="page-btn">2</button>
                        <button class="page-btn">3</button>
                        <button class="page-btn"><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>

                <!-- Add/Edit Portfolio Modal -->
                <div class="modal" id="portfolioModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Add New Project</h3>
                            <button class="close-modal"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="modal-body">
                            <form class="admin-form" id="portfolioForm">
                                <div class="form-row" style="display: flex; gap: 20px;">
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Project Name</label>
                                        <input type="text" class="form-control" placeholder="Enter project name">
                                    </div>
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Category</label>
                                        <select class="form-select">
                                            <option>Web Development</option>
                                            <option>App Development</option>
                                            <option>Graphic Design</option>
                                            <option>Digital Marketing</option>
                                            <option>Video Editing</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-row" style="display: flex; gap: 20px;">
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Client Name</label>
                                        <input type="text" class="form-control" placeholder="Enter client name">
                                    </div>
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Completion Date</label>
                                        <input type="date" class="form-control">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Short Description</label>
                                    <input type="text" class="form-control" placeholder="Enter short description">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Detailed Description</label>
                                    <textarea class="form-control" placeholder="Enter detailed description" rows="5"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Project Images</label>
                                    <div class="image-upload-container">
                                        <div class="image-upload">
                                            <input type="file" id="featuredImage" class="file-input">
                                            <label for="featuredImage" class="upload-label">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                                <span>Upload Featured Image</span>
                                            </label>
                                        </div>
                                        <div class="image-upload">
                                            <input type="file" id="galleryImages" class="file-input" multiple>
                                            <label for="galleryImages" class="upload-label">
                                                <i class="fas fa-images"></i>
                                                <span>Upload Gallery Images</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Project URL (Optional)</label>
                                    <input type="url" class="form-control" placeholder="Enter project URL">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Technologies Used</label>
                                    <input type="text" class="form-control" placeholder="Enter technologies (comma separated)">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Status</label>
                                    <div class="form-check">
                                        <input type="radio" name="status" id="statusCompleted" class="form-check-input" checked>
                                        <label for="statusCompleted">Completed</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" name="status" id="statusProgress" class="form-check-input">
                                        <label for="statusProgress">In Progress</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" name="status" id="statusDraft" class="form-check-input">
                                        <label for="statusDraft">Draft</label>
                                    </div>
                                </div>
                                
                                <div class="form-buttons">
                                    <button type="submit" class="btn btn-primary">Save Project</button>
                                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // View toggle functionality
        const viewBtns = document.querySelectorAll('.view-btn');
        const gridView = document.getElementById('grid-view');
        const listView = document.getElementById('list-view');
        
        viewBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const viewType = btn.getAttribute('data-view');
                
                // Remove active class from all buttons
                viewBtns.forEach(b => b.classList.remove('active'));
                
                // Add active class to clicked button
                btn.classList.add('active');
                
                // Show selected view
                if (viewType === 'grid') {
                    gridView.classList.add('active-view');
                    listView.classList.remove('active-view');
                } else {
                    listView.classList.add('active-view');
                    gridView.classList.remove('active-view');
                }
            });
        });
        
        // Modal functionality
        const modal = document.getElementById('portfolioModal');
        const addPortfolioBtn = document.getElementById('addPortfolioBtn');
        const closeModalBtns = document.querySelectorAll('.close-modal');
        const editPortfolioBtns = document.querySelectorAll('.edit-portfolio');
        
        addPortfolioBtn.addEventListener('click', () => {
            modal.style.display = 'flex';
        });
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        });
        
        editPortfolioBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // In a real application, you would fetch the portfolio data here
                document.querySelector('.modal-header h3').textContent = 'Edit Project';
                modal.style.display = 'flex';
            });
        });
        
        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
        
        // Tab functionality
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                // In a real application, you would filter the portfolio items here
            });
        });
    </script>
</body>
</html>