/**
 * DevGlobal - Admin Panel JavaScript
 * This file handles all interactive elements and functionality of the admin panel
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS animation library
    AOS.init({
        duration: 800,
        easing: 'ease',
        once: true,
        offset: 50
    });
    
    // Mobile sidebar toggle for responsive design
    const adminContainer = document.querySelector('.admin-container');
    const adminHeader = document.querySelector('.admin-header');
    
    if (adminHeader) {
        // Create menu toggle button
        const menuToggle = document.createElement('button');
        menuToggle.classList.add('menu-toggle');
        menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
        adminHeader.prepend(menuToggle);
        
        // Create sidebar overlay
        const sidebarOverlay = document.createElement('div');
        sidebarOverlay.classList.add('sidebar-overlay');
        adminContainer.appendChild(sidebarOverlay);
        
        // Toggle sidebar when menu button is clicked
        menuToggle.addEventListener('click', function() {
            adminContainer.classList.toggle('sidebar-collapsed');
            // Ensure content is accessible when sidebar is toggled
            document.body.style.overflow = adminContainer.classList.contains('sidebar-collapsed') ? 'auto' : 'hidden';
        });
        
        // Close sidebar when clicking on overlay
        sidebarOverlay.addEventListener('click', function() {
            adminContainer.classList.add('sidebar-collapsed');
            // Restore scrolling when sidebar is closed
            document.body.style.overflow = 'auto';
        });
        
        // Auto-collapse sidebar on small screens
        function checkScreenSize() {
            if (window.innerWidth < 992) {
                adminContainer.classList.add('sidebar-collapsed');
                document.body.style.overflow = 'auto';
            } else {
                adminContainer.classList.remove('sidebar-collapsed');
                document.body.style.overflow = 'auto';
            }
        }
        
        // Check on load and resize
        checkScreenSize();
        window.addEventListener('resize', checkScreenSize);
    }
    
    // Notifications dropdown
    const notificationsEl = document.querySelector('.notifications');
    if (notificationsEl) {
        // Create dropdown content
        const dropdown = document.createElement('div');
        dropdown.classList.add('dropdown-content');
        dropdown.innerHTML = `
            <div class="dropdown-header">
                <h4>Notifications</h4>
                <a href="#" class="mark-all-read">Mark all as read</a>
            </div>
            <div class="notification-list">
                <a href="#" class="notification-item unread">
                    <div class="notification-icon"><i class="fas fa-shopping-cart"></i></div>
                    <div class="notification-content">
                        <p class="notification-text">New order received: Simple Website Plan</p>
                        <p class="notification-time">2 hours ago</p>
                    </div>
                </a>
                <a href="#" class="notification-item unread">
                    <div class="notification-icon"><i class="fas fa-user"></i></div>
                    <div class="notification-content">
                        <p class="notification-text">New job application for Web Developer position</p>
                        <p class="notification-time">5 hours ago</p>
                    </div>
                </a>
                <a href="#" class="notification-item">
                    <div class="notification-icon"><i class="fas fa-comment"></i></div>
                    <div class="notification-content">
                        <p class="notification-text">New contact message: Inquiry about Advanced Plan</p>
                        <p class="notification-time">1 day ago</p>
                    </div>
                </a>
            </div>
            <div class="dropdown-footer">
                <a href="#">View all notifications</a>
            </div>
        `;
        notificationsEl.appendChild(dropdown);
        
        notificationsEl.addEventListener('click', function(e) {
            e.preventDefault();
            dropdown.classList.toggle('show');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!notificationsEl.contains(e.target)) {
                dropdown.classList.remove('show');
            }
        });
        
        // Mark all as read functionality
        const markAllReadBtn = dropdown.querySelector('.mark-all-read');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', function(e) {
                e.preventDefault();
                const unreadItems = dropdown.querySelectorAll('.notification-item.unread');
                unreadItems.forEach(item => item.classList.remove('unread'));
                
                // Update badge count
                const badge = notificationsEl.querySelector('.badge');
                if (badge) {
                    badge.textContent = '0';
                }
            });
        }
    }
    
    // Search functionality
    const searchInput = document.querySelector('.header-search input');
    if (searchInput) {
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                // Implement search functionality here
                console.log('Searching for:', searchInput.value);
                // In a real application, you would make an API call here
            }
        });
    }
    
    // Modal functionality for all admin pages
    function setupModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const closeButtons = modal.querySelectorAll('.close-modal');
            
            // Close button functionality
            closeButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    modal.style.display = 'none';
                });
            });
            
            // Close when clicking outside
            window.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
    }
    setupModals();
    
    // Tab functionality for all admin pages
    function setupTabs() {
        const tabContainers = document.querySelectorAll('.tabs-container');
        tabContainers.forEach(container => {
            const tabs = container.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs
                    tabs.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    tab.classList.add('active');
                    
                    // Update content based on tab (in a real app, this would filter data)
                    const category = tab.dataset.category || tab.dataset.status;
                    const sectionHeader = document.querySelector('.section-header h3');
                    if (sectionHeader) {
                        if (category === 'all') {
                            sectionHeader.textContent = 'All ' + (tab.dataset.category ? 'Services' : 'Orders');
                        } else {
                            sectionHeader.textContent = tab.textContent;
                        }
                    }
                });
            });
        });
    }
    setupTabs();
    
    // Form validation for all admin forms
    function setupFormValidation() {
        const forms = document.querySelectorAll('.admin-form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                let isValid = true;
                
                // Basic validation
                const requiredInputs = form.querySelectorAll('input[required], select[required], textarea[required]');
                requiredInputs.forEach(input => {
                    if (!input.value.trim()) {
                        isValid = false;
                        input.classList.add('error');
                    } else {
                        input.classList.remove('error');
                    }
                });
                
                if (isValid) {
                    // In a real application, you would submit the form data to an API
                    console.log('Form submitted successfully');
                    
                    // Close modal if form is in a modal
                    const modal = form.closest('.modal');
                    if (modal) {
                        modal.style.display = 'none';
                    }
                    
                    // Show success message
                    showNotification('Success', 'Form submitted successfully', 'success');
                } else {
                    showNotification('Error', 'Please fill in all required fields', 'error');
                }
            });
        });
    }
    setupFormValidation();
    
    // Delete confirmation for all delete buttons
    function setupDeleteConfirmations() {
        const deleteButtons = document.querySelectorAll('.delete-service, .delete-order');
        deleteButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.dataset.id;
                const type = this.classList.contains('delete-service') ? 'service' : 'order';
                
                if (confirm(`Are you sure you want to delete this ${type}?`)) {
                    // In a real application, you would make an API call to delete the item
                    console.log(`Deleting ${type} with ID: ${id}`);
                    
                    // Remove the row from the table (for demo purposes)
                    const row = this.closest('tr');
                    if (row) {
                        row.remove();
                    }
                    
                    showNotification('Success', `${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully`, 'success');
                }
            });
        });
    }
    setupDeleteConfirmations();
    
    // Notification system
    function showNotification(title, message, type = 'info') {
        const notification = document.createElement('div');
        notification.classList.add('admin-notification', `notification-${type}`);
        
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            </div>
            <div class="notification-content">
                <h4>${title}</h4>
                <p>${message}</p>
            </div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        `;
        
        // Add to the DOM
        if (!document.querySelector('.notification-container')) {
            const container = document.createElement('div');
            container.classList.add('notification-container');
            document.body.appendChild(container);
        }
        
        const container = document.querySelector('.notification-container');
        container.appendChild(notification);
        
        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Auto-hide after 5 seconds
        const hideTimeout = setTimeout(() => {
            hideNotification(notification);
        }, 5000);
        
        // Close button
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            clearTimeout(hideTimeout);
            hideNotification(notification);
        });
    }
    
    function hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
            
            // Remove container if empty
            const container = document.querySelector('.notification-container');
            if (container && container.children.length === 0) {
                container.remove();
            }
        }, 300);
    }
    
    // Dashboard charts (if on dashboard page)
    const dashboardContent = document.querySelector('.dashboard-content');
    if (dashboardContent && window.location.pathname.includes('index.html')) {
        // This would typically use a charting library like Chart.js
        console.log('Dashboard page detected - charts would be initialized here');
    }
    
    // Service page specific functionality
    if (window.location.pathname.includes('services.html')) {
        const addServiceBtn = document.getElementById('addServiceBtn');
        const serviceModal = document.getElementById('serviceModal');
        const editServiceBtns = document.querySelectorAll('.edit-service');
        
        if (addServiceBtn && serviceModal) {
            addServiceBtn.addEventListener('click', () => {
                document.querySelector('.modal-header h3').textContent = 'Add New Service';
                document.getElementById('serviceForm').reset();
                serviceModal.style.display = 'flex';
            });
        }
        
        if (editServiceBtns.length > 0 && serviceModal) {
            editServiceBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const serviceId = btn.dataset.id;
                    document.querySelector('.modal-header h3').textContent = 'Edit Service';
                    
                    // In a real application, you would fetch the service data here
                    console.log(`Fetching service data for ID: ${serviceId}`);
                    
                    // For demo purposes, pre-fill the form with dummy data
                    const form = document.getElementById('serviceForm');
                    if (form) {
                        // Simulate fetched data
                        setTimeout(() => {
                            const inputs = form.querySelectorAll('input, select, textarea');
                            inputs[0].value = 'Service Name ' + serviceId;
                            serviceModal.style.display = 'flex';
                        }, 300);
                    }
                });
            });
        }
    }
    
    // Orders page specific functionality
    if (window.location.pathname.includes('orders.html')) {
        const addOrderBtn = document.getElementById('addOrderBtn');
        const orderModal = document.getElementById('orderModal');
        const viewOrderBtns = document.querySelectorAll('.view-order');
        const editOrderBtns = document.querySelectorAll('.edit-order');
        
        if (addOrderBtn && orderModal) {
            addOrderBtn.addEventListener('click', () => {
                document.querySelector('.modal-header h3').textContent = 'Add New Order';
                document.getElementById('orderForm').reset();
                orderModal.style.display = 'flex';
            });
        }
        
        if (viewOrderBtns.length > 0) {
            viewOrderBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const orderId = btn.dataset.id;
                    
                    // In a real application, you would fetch the order details here
                    console.log(`Viewing order details for ID: ${orderId}`);
                    
                    // For demo purposes, show a modal with order details
                    const orderDetailsModal = document.getElementById('orderDetailsModal');
                    if (orderDetailsModal) {
                        document.querySelector('#orderDetailsModal .order-id').textContent = orderId;
                        orderDetailsModal.style.display = 'flex';
                    }
                });
            });
        }
        
        if (editOrderBtns.length > 0 && orderModal) {
            editOrderBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const orderId = btn.dataset.id;
                    document.querySelector('.modal-header h3').textContent = 'Edit Order';
                    
                    // In a real application, you would fetch the order data here
                    console.log(`Fetching order data for ID: ${orderId}`);
                    
                    // For demo purposes, pre-fill the form with dummy data
                    const form = document.getElementById('orderForm');
                    if (form) {
                        // Simulate fetched data
                        setTimeout(() => {
                            const inputs = form.querySelectorAll('input, select, textarea');
                            inputs[0].value = orderId;
                            orderModal.style.display = 'flex';
                        }, 300);
                    }
                });
            });
        }
        
        // Order status update functionality
        const statusSelects = document.querySelectorAll('.status-select');
        statusSelects.forEach(select => {
            select.addEventListener('change', function() {
                const orderId = this.dataset.id;
                const newStatus = this.value;
                
                // In a real application, you would update the order status via API
                console.log(`Updating order ${orderId} status to ${newStatus}`);
                
                // Update the status badge for demo purposes
                const statusBadge = this.closest('tr').querySelector('.status-badge');
                if (statusBadge) {
                    statusBadge.className = 'status-badge';
                    statusBadge.classList.add(`status-${newStatus.toLowerCase()}`);
                    statusBadge.textContent = newStatus;
                }
                
                showNotification('Success', `Order status updated to ${newStatus}`, 'success');
            });
        });
    }
    
    // Settings page specific functionality
    if (window.location.pathname.includes('settings.html')) {
        const settingsForms = document.querySelectorAll('.settings-form');
        settingsForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // In a real application, you would save the settings via API
                console.log('Saving settings');
                
                showNotification('Success', 'Settings saved successfully', 'success');
            });
        });
    }
    
    // Logout functionality
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to logout?')) {
                // In a real application, you would handle logout via API
                console.log('Logging out');
                
                // Redirect to login page
                // window.location.href = '../index.html';
                showNotification('Success', 'You have been logged out successfully', 'success');
            }
        });
    }
});