<?php
/**
 * Admin Dashboard - No Authentication Required (for testing)
 * This is a temporary version of the admin dashboard without authentication
 */

require_once '../config/config.php';

// Create a fake admin session for testing
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 1;
    $_SESSION['admin_username'] = 'admin';
    $_SESSION['admin_role'] = 'super_admin';
    $_SESSION['admin_login_time'] = time();
}

// Create fake admin data
$currentAdmin = [
    'id' => 1,
    'username' => 'admin',
    'email' => '<EMAIL>',
    'full_name' => 'Admin User',
    'role' => 'super_admin'
];

// Get dashboard statistics (with error handling)
$stats = [];

try {
    $newOrders = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending' AND created_at > DATE_SUB(NOW(), INTERVAL 1 WEEK)");
    $stats['new_orders'] = $newOrders['count'] ?? 0;
} catch (Exception $e) {
    $stats['new_orders'] = 0;
}

try {
    $jobApplications = fetchOne("SELECT COUNT(*) as count FROM job_applications WHERE status = 'new' AND created_at > DATE_SUB(NOW(), INTERVAL 1 WEEK)");
    $stats['job_applications'] = $jobApplications['count'] ?? 0;
} catch (Exception $e) {
    $stats['job_applications'] = 0;
}

try {
    $revenue = fetchOne("SELECT SUM(total_amount) as total FROM orders WHERE payment_status = 'paid' AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')");
    $stats['revenue'] = $revenue['total'] ?? 0;
} catch (Exception $e) {
    $stats['revenue'] = 0;
}

try {
    $activeProjects = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status IN ('confirmed', 'in-progress')");
    $stats['active_projects'] = $activeProjects['count'] ?? 0;
} catch (Exception $e) {
    $stats['active_projects'] = 0;
}

try {
    $contactMessages = fetchOne("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'");
    $stats['contact_messages'] = $contactMessages['count'] ?? 0;
} catch (Exception $e) {
    $stats['contact_messages'] = 0;
}

// Get recent data (with error handling)
try {
    $recentOrders = fetchAll("SELECT * FROM orders ORDER BY created_at DESC LIMIT 3");
} catch (Exception $e) {
    $recentOrders = [];
}

try {
    $recentMessages = fetchAll("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 4");
} catch (Exception $e) {
    $recentMessages = [];
}

try {
    $recentApplications = fetchAll("SELECT ja.*, jp.title as job_title FROM job_applications ja LEFT JOIN job_positions jp ON ja.job_id = jp.id ORDER BY ja.created_at DESC LIMIT 2");
} catch (Exception $e) {
    $recentApplications = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - DevGlobal</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .demo-notice {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }
        
        .demo-notice i {
            margin-right: 10px;
        }
    </style>
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h1>DevGlobal</h1>
                <p>Admin Panel</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="services.php">
                            <i class="fas fa-cogs"></i>
                            <span>Services</span>
                        </a>
                    </li>
                    <li>
                        <a href="pricing.php">
                            <i class="fas fa-tags"></i>
                            <span>Pricing Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="orders.php">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li>
                        <a href="hiring.php">
                            <i class="fas fa-users"></i>
                            <span>Hiring</span>
                        </a>
                    </li>
                    <li>
                        <a href="portfolio.php">
                            <i class="fas fa-briefcase"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.php">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.php" class="logout-btn">
                    <i class="fas fa-home"></i>
                    <span>Back to Website</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Top Header -->
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search...">
                </div>
                <div class="header-actions">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge"><?php echo $stats['contact_messages']; ?></span>
                    </div>
                    <div class="admin-profile">
                        <img src="https://placehold.co/100x100" alt="Admin">
                        <span><?php echo htmlspecialchars($currentAdmin['full_name'] ?: $currentAdmin['username']); ?></span>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <!-- Demo Notice -->
                <div class="demo-notice">
                    <i class="fas fa-info-circle"></i>
                    <strong>Demo Mode:</strong> This is a test version of the admin panel. Authentication is bypassed for testing purposes.
                    <a href="login.php" style="margin-left: 15px;">Go to Login Page</a>
                </div>
                
                <div class="page-header">
                    <h2>Dashboard</h2>
                    <p>Welcome to DevGlobal Admin Panel</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['new_orders']; ?></h3>
                            <p>New Orders</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">This week</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, $stats['new_orders'] * 10); ?>%;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['job_applications']; ?></h3>
                            <p>Job Applications</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">This week</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, $stats['job_applications'] * 15); ?>%;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo formatCurrency($stats['revenue']); ?></h3>
                            <p>Revenue</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">This month</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, ($stats['revenue'] / 1000) * 2); ?>%;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['active_projects']; ?></h3>
                            <p>Active Projects</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">Currently active</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, $stats['active_projects'] * 20); ?>%;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['contact_messages']; ?></h3>
                            <p>New Messages</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">Unread</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, $stats['contact_messages'] * 25); ?>%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h3>Quick Actions</h3>
                    </div>
                    <div class="quick-actions" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <a href="services.php" class="quick-action" style="display: flex; flex-direction: column; align-items: center; padding: 20px; background: #f8f9fa; border-radius: 8px; text-decoration: none; color: var(--text-primary); transition: all 0.3s ease;">
                            <i class="fas fa-plus-circle" style="font-size: 1.5rem; margin-bottom: 8px;"></i>
                            <span>Manage Services</span>
                        </a>
                        <a href="../api/services.php" class="quick-action" style="display: flex; flex-direction: column; align-items: center; padding: 20px; background: #f8f9fa; border-radius: 8px; text-decoration: none; color: var(--text-primary); transition: all 0.3s ease;">
                            <i class="fas fa-code" style="font-size: 1.5rem; margin-bottom: 8px;"></i>
                            <span>Test API</span>
                        </a>
                        <a href="../test_connection.php" class="quick-action" style="display: flex; flex-direction: column; align-items: center; padding: 20px; background: #f8f9fa; border-radius: 8px; text-decoration: none; color: var(--text-primary); transition: all 0.3s ease;">
                            <i class="fas fa-stethoscope" style="font-size: 1.5rem; margin-bottom: 8px;"></i>
                            <span>System Test</span>
                        </a>
                        <a href="../setup.php" class="quick-action" style="display: flex; flex-direction: column; align-items: center; padding: 20px; background: #f8f9fa; border-radius: 8px; text-decoration: none; color: var(--text-primary); transition: all 0.3s ease;">
                            <i class="fas fa-tools" style="font-size: 1.5rem; margin-bottom: 8px;"></i>
                            <span>Setup Wizard</span>
                        </a>
                    </div>
                </div>

                <!-- System Information -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h3>System Information</h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h4 style="margin-top: 0; color: var(--primary);">Database Status</h4>
                            <p style="margin-bottom: 0;">
                                <?php 
                                try {
                                    $db = getDB();
                                    echo '<span style="color: var(--success);"><i class="fas fa-check-circle"></i> Connected</span>';
                                } catch (Exception $e) {
                                    echo '<span style="color: var(--danger);"><i class="fas fa-times-circle"></i> Failed</span>';
                                }
                                ?>
                            </p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h4 style="margin-top: 0; color: var(--primary);">PHP Version</h4>
                            <p style="margin-bottom: 0;"><?php echo PHP_VERSION; ?></p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h4 style="margin-top: 0; color: var(--primary);">Server Time</h4>
                            <p style="margin-bottom: 0;"><?php echo date('Y-m-d H:i:s'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
    <script>
        // Initialize AOS
        AOS.init();
        
        // Add hover effects to quick actions
        document.querySelectorAll('.quick-action').forEach(action => {
            action.addEventListener('mouseenter', function() {
                this.style.background = 'var(--primary)';
                this.style.color = 'white';
                this.style.transform = 'translateY(-2px)';
            });
            
            action.addEventListener('mouseleave', function() {
                this.style.background = '#f8f9fa';
                this.style.color = 'var(--text-primary)';
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
