<?php
/**
 * Admin Logout Script
 */

require_once '../config/config.php';

// Check if user is logged in
if (isAdminLoggedIn()) {
    // Log the logout activity
    logAdminActivity('Logged out', 'Admin logged out');
    
    // Clear all session data
    session_unset();
    session_destroy();
    
    // Start a new session for the success message
    session_start();
    $_SESSION['logout_message'] = 'You have been successfully logged out.';
}

// Redirect to login page
redirect('login.php');
?>
