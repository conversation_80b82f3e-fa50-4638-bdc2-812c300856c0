<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - DevGlobal Admin</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h1>DevGlobal</h1>
                <p>Admin Panel</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="services.html">
                            <i class="fas fa-cogs"></i>
                            <span>Services</span>
                        </a>
                    </li>
                    <li>
                        <a href="pricing.html">
                            <i class="fas fa-tags"></i>
                            <span>Pricing Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="orders.html">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li>
                        <a href="hiring.html">
                            <i class="fas fa-users"></i>
                            <span>Hiring</span>
                        </a>
                    </li>
                    <li>
                        <a href="portfolio.html">
                            <i class="fas fa-briefcase"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Top Header -->
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search settings...">
                </div>
                <div class="header-actions">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="admin-profile">
                        <img src="https://placehold.co/100x100" alt="Admin">
                        <span>Admin User</span>
                    </div>
                </div>
            </header>

            <!-- Settings Content -->
            <div class="dashboard-content">
                <div class="page-header">
                    <h2>Settings</h2>
                    <p>Manage your account and system settings</p>
                </div>

                <!-- Settings Tabs -->
                <div class="tabs-container" style="margin-bottom: 25px;">
                    <div class="tabs">
                        <button class="tab active" data-tab="account">Account Settings</button>
                        <button class="tab" data-tab="profile">Profile</button>
                        <button class="tab" data-tab="security">Security</button>
                        <button class="tab" data-tab="notifications">Notifications</button>
                        <button class="tab" data-tab="system">System</button>
                    </div>
                </div>

                <!-- Account Settings Tab Content -->
                <div class="tab-content active" id="account-tab">
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h3>Account Information</h3>
                        </div>
                        <div class="settings-form">
                            <form>
                                <div class="form-group">
                                    <label for="fullName">Full Name</label>
                                    <input type="text" id="fullName" class="form-control" value="Admin User">
                                </div>
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" id="email" class="form-control" value="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" class="form-control" value="+****************">
                                </div>
                                <div class="form-group">
                                    <label for="role">Role</label>
                                    <select id="role" class="form-control">
                                        <option value="admin" selected>Administrator</option>
                                        <option value="manager">Manager</option>
                                        <option value="editor">Editor</option>
                                    </select>
                                </div>
                                <div class="form-buttons">
                                    <button type="submit" class="btn btn-primary">Save Changes</button>
                                    <button type="reset" class="btn btn-secondary">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Profile Tab Content -->
                <div class="tab-content" id="profile-tab">
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h3>Profile Settings</h3>
                        </div>
                        <div class="settings-form">
                            <div class="profile-picture-section">
                                <div class="current-picture">
                                    <img src="https://placehold.co/200x200" alt="Profile Picture">
                                </div>
                                <div class="picture-actions">
                                    <button class="btn btn-primary">Upload New Picture</button>
                                    <button class="btn btn-outline">Remove Picture</button>
                                </div>
                                <p class="form-hint">Recommended size: 200x200px. Max file size: 2MB</p>
                            </div>
                            <form>
                                <div class="form-group">
                                    <label for="bio">Bio</label>
                                    <textarea id="bio" class="form-control" rows="4">Administrator at DevGlobal with over 5 years of experience in web development and project management.</textarea>
                                </div>
                                <div class="form-group">
                                    <label for="location">Location</label>
                                    <input type="text" id="location" class="form-control" value="New York, USA">
                                </div>
                                <div class="form-group">
                                    <label for="website">Website</label>
                                    <input type="url" id="website" class="form-control" value="https://devglobal.com">
                                </div>
                                <div class="form-buttons">
                                    <button type="submit" class="btn btn-primary">Save Profile</button>
                                    <button type="reset" class="btn btn-secondary">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Security Tab Content -->
                <div class="tab-content" id="security-tab">
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h3>Security Settings</h3>
                        </div>
                        <div class="settings-form">
                            <form>
                                <div class="form-group">
                                    <label for="currentPassword">Current Password</label>
                                    <input type="password" id="currentPassword" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="newPassword">New Password</label>
                                    <input type="password" id="newPassword" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="confirmPassword">Confirm New Password</label>
                                    <input type="password" id="confirmPassword" class="form-control">
                                </div>
                                <div class="form-buttons">
                                    <button type="submit" class="btn btn-primary">Update Password</button>
                                    <button type="reset" class="btn btn-secondary">Cancel</button>
                                </div>
                            </form>
                            
                            <div class="security-section" style="margin-top: 30px;">
                                <h4>Two-Factor Authentication</h4>
                                <p>Add an extra layer of security to your account</p>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="twoFactorToggle" class="toggle-input">
                                    <label for="twoFactorToggle" class="toggle-label"></label>
                                    <span>Enable Two-Factor Authentication</span>
                                </div>
                            </div>
                            
                            <div class="security-section" style="margin-top: 30px;">
                                <h4>Login Sessions</h4>
                                <p>Manage your active sessions</p>
                                <div class="sessions-list">
                                    <div class="session-item">
                                        <div class="session-info">
                                            <i class="fas fa-desktop"></i>
                                            <div>
                                                <h5>Windows 10 - Chrome</h5>
                                                <p>New York, USA - Current Session</p>
                                            </div>
                                        </div>
                                        <button class="btn btn-sm btn-outline">This Device</button>
                                    </div>
                                    <div class="session-item">
                                        <div class="session-info">
                                            <i class="fas fa-mobile-alt"></i>
                                            <div>
                                                <h5>iPhone 13 - Safari</h5>
                                                <p>New York, USA - Last active: 2 hours ago</p>
                                            </div>
                                        </div>
                                        <button class="btn btn-sm btn-danger">Logout</button>
                                    </div>
                                </div>
                                <button class="btn btn-danger" style="margin-top: 15px;">Logout from All Devices</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Tab Content -->
                <div class="tab-content" id="notifications-tab">
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h3>Notification Settings</h3>
                        </div>
                        <div class="settings-form">
                            <div class="notification-group">
                                <h4>Email Notifications</h4>
                                <div class="form-check">
                                    <input type="checkbox" id="emailOrders" class="form-check-input" checked>
                                    <label for="emailOrders">New orders and purchases</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="emailApplications" class="form-check-input" checked>
                                    <label for="emailApplications">New job applications</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="emailMessages" class="form-check-input" checked>
                                    <label for="emailMessages">New messages and comments</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="emailUpdates" class="form-check-input">
                                    <label for="emailUpdates">System updates and announcements</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="emailMarketing" class="form-check-input">
                                    <label for="emailMarketing">Marketing and promotional emails</label>
                                </div>
                            </div>
                            
                            <div class="notification-group" style="margin-top: 30px;">
                                <h4>System Notifications</h4>
                                <div class="form-check">
                                    <input type="checkbox" id="sysOrders" class="form-check-input" checked>
                                    <label for="sysOrders">New orders and purchases</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="sysApplications" class="form-check-input" checked>
                                    <label for="sysApplications">New job applications</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="sysMessages" class="form-check-input" checked>
                                    <label for="sysMessages">New messages and comments</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" id="sysUpdates" class="form-check-input" checked>
                                    <label for="sysUpdates">System updates and announcements</label>
                                </div>
                            </div>
                            
                            <div class="form-buttons">
                                <button type="submit" class="btn btn-primary">Save Preferences</button>
                                <button type="reset" class="btn btn-secondary">Reset to Default</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Tab Content -->
                <div class="tab-content" id="system-tab">
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h3>System Settings</h3>
                        </div>
                        <div class="settings-form">
                            <div class="system-group">
                                <h4>General Settings</h4>
                                <div class="form-group">
                                    <label for="siteName">Site Name</label>
                                    <input type="text" id="siteName" class="form-control" value="DevGlobal">
                                </div>
                                <div class="form-group">
                                    <label for="siteDescription">Site Description</label>
                                    <textarea id="siteDescription" class="form-control" rows="2">Professional web development and digital services agency.</textarea>
                                </div>
                                <div class="form-group">
                                    <label for="timezone">Timezone</label>
                                    <select id="timezone" class="form-control">
                                        <option value="utc-8">Pacific Time (UTC-8)</option>
                                        <option value="utc-7">Mountain Time (UTC-7)</option>
                                        <option value="utc-6">Central Time (UTC-6)</option>
                                        <option value="utc-5" selected>Eastern Time (UTC-5)</option>
                                        <option value="utc">UTC</option>
                                        <option value="utc+1">Central European Time (UTC+1)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="dateFormat">Date Format</label>
                                    <select id="dateFormat" class="form-control">
                                        <option value="mm/dd/yyyy" selected>MM/DD/YYYY</option>
                                        <option value="dd/mm/yyyy">DD/MM/YYYY</option>
                                        <option value="yyyy-mm-dd">YYYY-MM-DD</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="system-group" style="margin-top: 30px;">
                                <h4>API Settings</h4>
                                <div class="form-group">
                                    <label for="apiKey">API Key</label>
                                    <div class="input-with-button">
                                        <input type="text" id="apiKey" class="form-control" value="dg_api_7f4d8b2e9c1a5f3e6b7d9c2a5f3e6b7d" readonly>
                                        <button class="btn btn-sm btn-secondary">Regenerate</button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="webhookUrl">Webhook URL</label>
                                    <input type="url" id="webhookUrl" class="form-control" value="https://devglobal.com/api/webhook">
                                </div>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="enableApi" class="toggle-input" checked>
                                    <label for="enableApi" class="toggle-label"></label>
                                    <span>Enable API Access</span>
                                </div>
                            </div>
                            
                            <div class="system-group" style="margin-top: 30px;">
                                <h4>Backup & Maintenance</h4>
                                <div class="backup-actions">
                                    <button class="btn btn-primary">Create Backup</button>
                                    <button class="btn btn-secondary">Restore from Backup</button>
                                </div>
                                <div class="toggle-switch" style="margin-top: 15px;">
                                    <input type="checkbox" id="maintenanceMode" class="toggle-input">
                                    <label for="maintenanceMode" class="toggle-label"></label>
                                    <span>Enable Maintenance Mode</span>
                                </div>
                            </div>
                            
                            <div class="form-buttons">
                                <button type="submit" class="btn btn-primary">Save System Settings</button>
                                <button type="reset" class="btn btn-secondary">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Initialize AOS animation
        AOS.init();
        
        // Tab switching functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs
                    tabs.forEach(t => t.classList.remove('active'));
                    
                    // Add active class to clicked tab
                    tab.classList.add('active');
                    
                    // Hide all tab contents
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    // Show the corresponding tab content
                    const tabId = tab.getAttribute('data-tab');
                    document.getElementById(`${tabId}-tab`).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>