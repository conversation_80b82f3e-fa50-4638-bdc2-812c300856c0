<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orders Management - DevGlobal Admin</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h1>DevGlobal</h1>
                <p>Admin Panel</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="services.html">
                            <i class="fas fa-cogs"></i>
                            <span>Services</span>
                        </a>
                    </li>
                    <li>
                        <a href="pricing.html">
                            <i class="fas fa-tags"></i>
                            <span>Pricing Plans</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="orders.html">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li>
                        <a href="hiring.html">
                            <i class="fas fa-users"></i>
                            <span>Hiring</span>
                        </a>
                    </li>
                    <li>
                        <a href="portfolio.html">
                            <i class="fas fa-briefcase"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Top Header -->
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search orders...">
                </div>
                <div class="header-actions">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="admin-profile">
                        <img src="https://placehold.co/100x100" alt="Admin">
                        <span>Admin User</span>
                    </div>
                </div>
            </header>

            <!-- Orders Content -->
            <div class="dashboard-content">
                <div class="page-header">
                    <h2>Orders Management</h2>
                    <p>Track and manage customer orders</p>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons" style="margin-bottom: 25px;">
                    <button class="btn btn-primary" id="addOrderBtn">
                        <i class="fas fa-plus"></i> Add New Order
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-file-export"></i> Export Orders
                    </button>
                </div>

                <!-- Orders Status Tabs -->
                <div class="tabs-container" style="margin-bottom: 25px;">
                    <div class="tabs">
                        <button class="tab active" data-status="all">All Orders</button>
                        <button class="tab" data-status="pending">Pending</button>
                        <button class="tab" data-status="progress">In Progress</button>
                        <button class="tab" data-status="completed">Completed</button>
                        <button class="tab" data-status="cancelled">Cancelled</button>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h3>All Orders</h3>
                        <div class="header-actions">
                            <select class="form-select" style="width: auto; padding: 8px 30px 8px 15px;">
                                <option>Sort by: Newest</option>
                                <option>Sort by: Oldest</option>
                                <option>Sort by: Amount (High to Low)</option>
                                <option>Sort by: Amount (Low to High)</option>
                            </select>
                        </div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Service/Plan</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#ORD-001</td>
                                <td>John Doe</td>
                                <td>Simple Website</td>
                                <td>₹1,200</td>
                                <td>15 Jun 2023</td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon view-order" data-id="ORD-001">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon edit-order" data-id="ORD-001">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-order" data-id="ORD-001">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>#ORD-002</td>
                                <td>Jane Smith</td>
                                <td>App Development</td>
                                <td>₹5,000</td>
                                <td>12 Jun 2023</td>
                                <td><span class="status-badge status-progress">In Progress</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon view-order" data-id="ORD-002">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon edit-order" data-id="ORD-002">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-order" data-id="ORD-002">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>#ORD-003</td>
                                <td>Robert Johnson</td>
                                <td>Digital Marketing</td>
                                <td>₹2,300</td>
                                <td>10 Jun 2023</td>
                                <td><span class="status-badge status-completed">Completed</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon view-order" data-id="ORD-003">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon edit-order" data-id="ORD-003">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-order" data-id="ORD-003">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>#ORD-004</td>
                                <td>Emily Davis</td>
                                <td>Standard Website</td>
                                <td>₹3,500</td>
                                <td>05 Jun 2023</td>
                                <td><span class="status-badge status-progress">In Progress</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon view-order" data-id="ORD-004">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon edit-order" data-id="ORD-004">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-order" data-id="ORD-004">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>#ORD-005</td>
                                <td>Michael Wilson</td>
                                <td>Logo Design</td>
                                <td>₹1,500</td>
                                <td>01 Jun 2023</td>
                                <td><span class="status-badge status-cancelled">Cancelled</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon view-order" data-id="ORD-005">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon edit-order" data-id="ORD-005">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-order" data-id="ORD-005">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination">
                        <button class="page-btn"><i class="fas fa-chevron-left"></i></button>
                        <button class="page-btn active">1</button>
                        <button class="page-btn">2</button>
                        <button class="page-btn">3</button>
                        <button class="page-btn"><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>

                <!-- Order Details Modal -->
                <div class="modal" id="orderDetailsModal">
                    <div class="modal-content" style="max-width: 800px;">
                        <div class="modal-header">
                            <h3>Order Details</h3>
                            <button class="close-modal"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="modal-body">
                            <div class="order-details">
                                <div class="order-header">
                                    <div class="order-id">
                                        <h4>Order #ORD-001</h4>
                                        <span class="status-badge status-pending">Pending</span>
                                    </div>
                                    <div class="order-date">
                                        <p>Placed on: 15 Jun 2023</p>
                                    </div>
                                </div>
                                
                                <div class="order-sections">
                                    <div class="order-section">
                                        <h5>Customer Information</h5>
                                        <div class="order-info-grid">
                                            <div class="info-item">
                                                <span class="info-label">Name:</span>
                                                <span class="info-value">John Doe</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Email:</span>
                                                <span class="info-value"><EMAIL></span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Phone:</span>
                                                <span class="info-value">+91 9876543210</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Company:</span>
                                                <span class="info-value">ABC Company</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="order-section">
                                        <h5>Order Details</h5>
                                        <div class="order-info-grid">
                                            <div class="info-item">
                                                <span class="info-label">Service:</span>
                                                <span class="info-value">Website Development</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Plan:</span>
                                                <span class="info-value">Simple</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Building Cost:</span>
                                                <span class="info-value">₹1,000</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Monthly Fee:</span>
                                                <span class="info-value">₹200</span>
                                            </div>
                                            <div class="info-item" style="grid-column: 1 / -1;">
                                                <span class="info-label">Requirements:</span>
                                                <span class="info-value">5-page website for a small business with contact form and mobile responsive design.</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="order-section">
                                        <h5>Payment Information</h5>
                                        <div class="order-info-grid">
                                            <div class="info-item">
                                                <span class="info-label">Payment Method:</span>
                                                <span class="info-value">Bank Transfer</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Payment Status:</span>
                                                <span class="info-value">Paid</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Transaction ID:</span>
                                                <span class="info-value">TXN123456789</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Payment Date:</span>
                                                <span class="info-value">15 Jun 2023</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="order-actions">
                                    <div class="status-update">
                                        <label>Update Status:</label>
                                        <select class="form-select">
                                            <option>Pending</option>
                                            <option>In Progress</option>
                                            <option>Completed</option>
                                            <option>Cancelled</option>
                                        </select>
                                        <button class="btn btn-primary">Update</button>
                                    </div>
                                    <div class="action-buttons">
                                        <button class="btn btn-secondary">
                                            <i class="fas fa-envelope"></i> Email Customer
                                        </button>
                                        <button class="btn btn-secondary">
                                            <i class="fas fa-print"></i> Print Order
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add/Edit Order Modal -->
                <div class="modal" id="orderModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Add New Order</h3>
                            <button class="close-modal"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="modal-body">
                            <form class="admin-form" id="orderForm">
                                <div class="form-section">
                                    <h4>Customer Information</h4>
                                    <div class="form-row" style="display: flex; gap: 20px;">
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Customer Name</label>
                                            <input type="text" class="form-control" placeholder="Enter customer name">
                                        </div>
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" placeholder="Enter email address">
                                        </div>
                                    </div>
                                    <div class="form-row" style="display: flex; gap: 20px;">
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Phone</label>
                                            <input type="tel" class="form-control" placeholder="Enter phone number">
                                        </div>
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Company (Optional)</label>
                                            <input type="text" class="form-control" placeholder="Enter company name">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-section">
                                    <h4>Order Details</h4>
                                    <div class="form-row" style="display: flex; gap: 20px;">
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Service</label>
                                            <select class="form-select" id="serviceSelect">
                                                <option>Select a service</option>
                                                <option>Website Development</option>
                                                <option>App Development</option>
                                                <option>Graphic Design</option>
                                                <option>Digital Marketing</option>
                                                <option>Video Editing</option>
                                            </select>
                                        </div>
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Plan</label>
                                            <select class="form-select" id="planSelect">
                                                <option>Select a plan</option>
                                                <!-- Plans will be populated based on selected service -->
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Requirements</label>
                                        <textarea class="form-control" placeholder="Enter project requirements"></textarea>
                                    </div>
                                </div>
                                
                                <div class="form-section">
                                    <h4>Payment Information</h4>
                                    <div class="form-row" style="display: flex; gap: 20px;">
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Payment Method</label>
                                            <select class="form-select">
                                                <option>Bank Transfer</option>
                                                <option>Credit Card</option>
                                                <option>PayPal</option>
                                                <option>Cash</option>
                                            </select>
                                        </div>
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Payment Status</label>
                                            <select class="form-select">
                                                <option>Pending</option>
                                                <option>Paid</option>
                                                <option>Partially Paid</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-row" style="display: flex; gap: 20px;">
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Transaction ID (Optional)</label>
                                            <input type="text" class="form-control" placeholder="Enter transaction ID">
                                        </div>
                                        <div class="form-group" style="flex: 1;">
                                            <label class="form-label">Order Status</label>
                                            <select class="form-select">
                                                <option>Pending</option>
                                                <option>In Progress</option>
                                                <option>Completed</option>
                                                <option>Cancelled</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-buttons">
                                    <button type="submit" class="btn btn-primary">Save Order</button>
                                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Modal functionality
        const orderModal = document.getElementById('orderModal');
        const orderDetailsModal = document.getElementById('orderDetailsModal');
        const addOrderBtn = document.getElementById('addOrderBtn');
        const closeModalBtns = document.querySelectorAll('.close-modal');
        const viewOrderBtns = document.querySelectorAll('.view-order');
        const editOrderBtns = document.querySelectorAll('.edit-order');
        
        addOrderBtn.addEventListener('click', () => {
            orderModal.style.display = 'flex';
        });
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                orderModal.style.display = 'none';
                orderDetailsModal.style.display = 'none';
            });
        });
        
        viewOrderBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // In a real application, you would fetch the order data here
                orderDetailsModal.style.display = 'flex';
            });
        });
        
        editOrderBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // In a real application, you would fetch the order data here
                document.querySelector('#orderModal .modal-header h3').textContent = 'Edit Order';
                orderModal.style.display = 'flex';
            });
        });
        
        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === orderModal) {
                orderModal.style.display = 'none';
            }
            if (e.target === orderDetailsModal) {
                orderDetailsModal.style.display = 'none';
            }
        });
        
        // Tab functionality
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                // In a real application, you would filter the orders here
            });
        });
        
        // Service selection changes available plans
        const serviceSelect = document.getElementById('serviceSelect');
        const planSelect = document.getElementById('planSelect');
        
        serviceSelect.addEventListener('change', () => {
            // Clear current options
            planSelect.innerHTML = '<option>Select a plan</option>';
            
            // Add new options based on selected service
            const service = serviceSelect.value;
            if (service === 'Website Development') {
                planSelect.innerHTML += `
                    <option>Simple</option>
                    <option>Standard</option>
                    <option>Advanced</option>
                `;
            } else if (service === 'App Development') {
                planSelect.innerHTML += `
                    <option>Basic</option>
                    <option>Premium</option>
                    <option>Enterprise</option>
                `;
            } else if (service === 'Digital Marketing') {
                planSelect.innerHTML += `
                    <option>SEO Package</option>
                    <option>Social Media Package</option>
                    <option>Complete Digital Package</option>
                `;
            }
            // Add more conditions for other services
        });
    </script>
</body>
</html>