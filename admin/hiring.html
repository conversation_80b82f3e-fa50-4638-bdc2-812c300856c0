<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hiring Management - DevGlobal Admin</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h1>DevGlobal</h1>
                <p>Admin Panel</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="services.html">
                            <i class="fas fa-cogs"></i>
                            <span>Services</span>
                        </a>
                    </li>
                    <li>
                        <a href="pricing.html">
                            <i class="fas fa-tags"></i>
                            <span>Pricing Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="orders.html">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="hiring.html">
                            <i class="fas fa-users"></i>
                            <span>Hiring</span>
                        </a>
                    </li>
                    <li>
                        <a href="portfolio.html">
                            <i class="fas fa-briefcase"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Top Header -->
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search applications...">
                </div>
                <div class="header-actions">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="admin-profile">
                        <img src="https://placehold.co/100x100" alt="Admin">
                        <span>Admin User</span>
                    </div>
                </div>
            </header>

            <!-- Hiring Content -->
            <div class="dashboard-content">
                <div class="page-header">
                    <h2>Hiring Management</h2>
                    <p>Manage job positions and applications</p>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons" style="margin-bottom: 25px;">
                    <button class="btn btn-primary" id="addPositionBtn">
                        <i class="fas fa-plus"></i> Add New Position
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-file-export"></i> Export Applications
                    </button>
                </div>

                <!-- Tabs for Job Positions and Applications -->
                <div class="tabs-container" style="margin-bottom: 25px;">
                    <div class="tabs">
                        <button class="tab active" data-tab="positions">Job Positions</button>
                        <button class="tab" data-tab="applications">Applications</button>
                    </div>
                </div>

                <!-- Job Positions Section -->
                <div class="tab-content active" id="positions-content">
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h3>Open Positions</h3>
                            <div class="header-actions">
                                <select class="form-select" style="width: auto; padding: 8px 30px 8px 15px;">
                                    <option>Sort by: Newest</option>
                                    <option>Sort by: Oldest</option>
                                    <option>Sort by: Applications (High to Low)</option>
                                </select>
                            </div>
                        </div>
                        
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Location</th>
                                    <th>Applications</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>JOB-001</td>
                                    <td>Web Developer</td>
                                    <td>Development</td>
                                    <td>Remote</td>
                                    <td>12</td>
                                    <td><span class="status-badge status-completed">Active</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-position" data-id="JOB-001">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-position" data-id="JOB-001">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-position" data-id="JOB-001">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>JOB-002</td>
                                    <td>UI/UX Designer</td>
                                    <td>Design</td>
                                    <td>Hybrid</td>
                                    <td>8</td>
                                    <td><span class="status-badge status-completed">Active</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-position" data-id="JOB-002">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-position" data-id="JOB-002">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-position" data-id="JOB-002">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>JOB-003</td>
                                    <td>Digital Marketing Specialist</td>
                                    <td>Marketing</td>
                                    <td>On-site</td>
                                    <td>5</td>
                                    <td><span class="status-badge status-completed">Active</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-position" data-id="JOB-003">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-position" data-id="JOB-003">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-position" data-id="JOB-003">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>JOB-004</td>
                                    <td>Project Manager</td>
                                    <td>Management</td>
                                    <td>On-site</td>
                                    <td>3</td>
                                    <td><span class="status-badge status-pending">Draft</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-position" data-id="JOB-004">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-position" data-id="JOB-004">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-position" data-id="JOB-004">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Applications Section -->
                <div class="tab-content" id="applications-content">
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h3>Job Applications</h3>
                            <div class="header-actions">
                                <select class="form-select" style="width: auto; padding: 8px 30px 8px 15px;">
                                    <option>All Positions</option>
                                    <option>Web Developer</option>
                                    <option>UI/UX Designer</option>
                                    <option>Digital Marketing Specialist</option>
                                </select>
                            </div>
                        </div>
                        
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Applicant</th>
                                    <th>Position</th>
                                    <th>Applied Date</th>
                                    <th>Experience</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>APP-001</td>
                                    <td>Rahul Sharma</td>
                                    <td>Web Developer</td>
                                    <td>10 Jun 2023</td>
                                    <td>3 years</td>
                                    <td><span class="status-badge status-pending">New</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-application" data-id="APP-001">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-application" data-id="APP-001">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-application" data-id="APP-001">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>APP-002</td>
                                    <td>Priya Patel</td>
                                    <td>UI/UX Designer</td>
                                    <td>08 Jun 2023</td>
                                    <td>2 years</td>
                                    <td><span class="status-badge status-progress">Interviewing</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-application" data-id="APP-002">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-application" data-id="APP-002">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-application" data-id="APP-002">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>APP-003</td>
                                    <td>Amit Kumar</td>
                                    <td>Web Developer</td>
                                    <td>05 Jun 2023</td>
                                    <td>5 years</td>
                                    <td><span class="status-badge status-progress">Shortlisted</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-application" data-id="APP-003">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-application" data-id="APP-003">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-application" data-id="APP-003">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>APP-004</td>
                                    <td>Neha Singh</td>
                                    <td>Digital Marketing Specialist</td>
                                    <td>01 Jun 2023</td>
                                    <td>4 years</td>
                                    <td><span class="status-badge status-completed">Hired</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-application" data-id="APP-004">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-application" data-id="APP-004">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-application" data-id="APP-004">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>APP-005</td>
                                    <td>Vikram Reddy</td>
                                    <td>Web Developer</td>
                                    <td>28 May 2023</td>
                                    <td>2 years</td>
                                    <td><span class="status-badge status-cancelled">Rejected</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon view-application" data-id="APP-005">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-icon edit-application" data-id="APP-005">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon delete-application" data-id="APP-005">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <div class="pagination">
                            <button class="page-btn"><i class="fas fa-chevron-left"></i></button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn"><i class="fas fa-chevron-right"></i></button>
                        </div>
                    </div>
                </div>

                <!-- Add/Edit Position Modal -->
                <div class="modal" id="positionModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Add New Position</h3>
                            <button class="close-modal"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="modal-body">
                            <form class="admin-form" id="positionForm">
                                <div class="form-row" style="display: flex; gap: 20px;">
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Position Title</label>
                                        <input type="text" class="form-control" placeholder="Enter position title">
                                    </div>
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Department</label>
                                        <select class="form-select">
                                            <option>Development</option>
                                            <option>Design</option>
                                            <option>Marketing</option>
                                            <option>Management</option>
                                            <option>Sales</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-row" style="display: flex; gap: 20px;">
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Location</label>
                                        <select class="form-select">
                                            <option>Remote</option>
                                            <option>On-site</option>
                                            <option>Hybrid</option>
                                        </select>
                                    </div>
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Employment Type</label>
                                        <select class="form-select">
                                            <option>Full-time</option>
                                            <option>Part-time</option>
                                            <option>Contract</option>
                                            <option>Internship</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-row" style="display: flex; gap: 20px;">
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Experience Required (years)</label>
                                        <input type="number" class="form-control" placeholder="Enter years of experience">
                                    </div>
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Salary Range (₹)</label>
                                        <div style="display: flex; gap: 10px;">
                                            <input type="number" class="form-control" placeholder="Min">
                                            <input type="number" class="form-control" placeholder="Max">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Job Description</label>
                                    <textarea class="form-control" placeholder="Enter job description" rows="5"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Requirements</label>
                                    <textarea class="form-control" placeholder="Enter job requirements" rows="5"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Status</label>
                                    <div class="form-check">
                                        <input type="radio" name="status" id="statusActive" class="form-check-input" checked>
                                        <label for="statusActive">Active</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" name="status" id="statusDraft" class="form-check-input">
                                        <label for="statusDraft">Draft</label>
                                    </div>
                                </div>
                                
                                <div class="form-buttons">
                                    <button type="submit" class="btn btn-primary">Save Position</button>
                                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- View Application Modal -->
                <div class="modal" id="applicationModal">
                    <div class="modal-content" style="max-width: 800px;">
                        <div class="modal-header">
                            <h3>Application Details</h3>
                            <button class="close-modal"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="modal-body">
                            <div class="application-details">
                                <div class="application-header">
                                    <div>
                                        <h4>Rahul Sharma</h4>
                                        <p>Web Developer Position</p>
                                    </div>
                                    <span class="status-badge status-pending">New</span>
                                </div>
                                
                                <div class="application-sections">
                                    <div class="application-section">
                                        <h5>Personal Information</h5>
                                        <div class="info-grid">
                                            <div class="info-item">
                                                <span class="info-label">Email:</span>
                                                <span class="info-value"><EMAIL></span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Phone:</span>
                                                <span class="info-value">+91 9876543210</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Location:</span>
                                                <span class="info-value">Mumbai, India</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Applied On:</span>
                                                <span class="info-value">10 Jun 2023</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="application-section">
                                        <h5>Professional Information</h5>
                                        <div class="info-grid">
                                            <div class="info-item">
                                                <span class="info-label">Experience:</span>
                                                <span class="info-value">3 years</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Current Company:</span>
                                                <span class="info-value">TechSolutions Ltd.</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Current Role:</span>
                                                <span class="info-value">Frontend Developer</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Expected Salary:</span>
                                                <span class="info-value">₹8,00,000 per annum</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="application-section">
                                        <h5>Skills</h5>
                                        <div class="skills-container">
                                            <span class="skill-badge">HTML5</span>
                                            <span class="skill-badge">CSS3</span>
                                            <span class="skill-badge">JavaScript</span>
                                            <span class="skill-badge">React.js</span>
                                            <span class="skill-badge">Node.js</span>
                                            <span class="skill-badge">MongoDB</span>
                                            <span class="skill-badge">Git</span>
                                            <span class="skill-badge">Responsive Design</span>
                                        </div>
                                    </div>
                                    
                                    <div class="application-section">
                                        <h5>Cover Letter</h5>
                                        <div class="cover-letter">
                                            <p>Dear Hiring Manager,</p>
                                            <p>I am writing to express my interest in the Web Developer position at DevGlobal. With 3 years of experience in frontend development, I have developed strong skills in creating responsive and user-friendly web applications using modern technologies.</p>
                                            <p>In my current role at TechSolutions Ltd., I have successfully delivered multiple projects for clients across various industries. I am particularly proud of developing an e-commerce platform that improved conversion rates by 25%.</p>
                                            <p>I am excited about the opportunity to bring my technical expertise and creative problem-solving skills to DevGlobal and contribute to your innovative projects.</p>
                                            <p>Thank you for considering my application.</p>
                                            <p>Sincerely,<br>Rahul Sharma</p>
                                        </div>
                                    </div>
                                    
                                    <div class="application-section">
                                        <h5>Attachments</h5>
                                        <div class="attachments">
                                            <a href="#" class="attachment">
                                                <i class="fas fa-file-pdf"></i>
                                                <span>Rahul_Sharma_Resume.pdf</span>
                                            </a>
                                            <a href="#" class="attachment">
                                                <i class="fas fa-file-image"></i>
                                                <span>Portfolio_Screenshot.jpg</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="application-actions">
                                    <div class="status-update">
                                        <label>Update Status:</label>
                                        <select class="form-select">
                                            <option>New</option>
                                            <option>Shortlisted</option>
                                            <option>Interviewing</option>
                                            <option>Hired</option>
                                            <option>Rejected</option>
                                        </select>
                                        <button class="btn btn-primary">Update</button>
                                    </div>
                                    <div class="action-buttons">
                                        <button class="btn btn-secondary">
                                            <i class="fas fa-envelope"></i> Email Candidate
                                        </button>
                                        <button class="btn btn-secondary">
                                            <i class="fas fa-calendar-alt"></i> Schedule Interview
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Tab switching functionality
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');
                
                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to current tab and content
                tab.classList.add('active');
                document.getElementById(`${tabId}-content`).classList.add('active');
            });
        });
        
        // Modal functionality
        const positionModal = document.getElementById('positionModal');
        const applicationModal = document.getElementById('applicationModal');
        const addPositionBtn = document.getElementById('addPositionBtn');
        const closeModalBtns = document.querySelectorAll('.close-modal');
        const viewPositionBtns = document.querySelectorAll('.view-position');
        const editPositionBtns = document.querySelectorAll('.edit-position');
        const viewApplicationBtns = document.querySelectorAll('.view-application');
        
        addPositionBtn.addEventListener('click', () => {
            positionModal.style.display = 'flex';
        });
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                positionModal.style.display = 'none';
                applicationModal.style.display = 'none';
            });
        });
        
        viewPositionBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // In a real application, you would fetch the position data here
                document.querySelector('#positionModal .modal-header h3').textContent = 'View Position';
                positionModal.style.display = 'flex';
            });
        });
        
        editPositionBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // In a real application, you would fetch the position data here
                document.querySelector('#positionModal .modal-header h3').textContent = 'Edit Position';
                positionModal.style.display = 'flex';
            });
        });
        
        viewApplicationBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // In a real application, you would fetch the application data here
                applicationModal.style.display = 'flex';
            });
        });
        
        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === positionModal) {
                positionModal.style.display = 'none';
            }
            if (e.target === applicationModal) {
                applicationModal.style.display = 'none';
            }
        });
    </script>
</body>
</html>