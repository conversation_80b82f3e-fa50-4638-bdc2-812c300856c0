<?php
require_once '../config/config.php';

// Check admin authentication
requireAdminLogin();

// Get current admin
$currentAdmin = getCurrentAdmin();

// Get dashboard statistics
$stats = [];
$newOrders = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending' AND created_at > DATE_SUB(NOW(), INTERVAL 1 WEEK)");
$stats['new_orders'] = $newOrders['count'] ?? 0;

$jobApplications = fetchOne("SELECT COUNT(*) as count FROM job_applications WHERE status = 'new' AND created_at > DATE_SUB(NOW(), INTERVAL 1 WEEK)");
$stats['job_applications'] = $jobApplications['count'] ?? 0;

$revenue = fetchOne("SELECT SUM(total_amount) as total FROM orders WHERE payment_status = 'paid' AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')");
$stats['revenue'] = $revenue['total'] ?? 0;

$activeProjects = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status IN ('confirmed', 'in-progress')");
$stats['active_projects'] = $activeProjects['count'] ?? 0;

$contactMessages = fetchOne("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'");
$stats['contact_messages'] = $contactMessages['count'] ?? 0;

// Get recent data
$recentOrders = fetchAll("SELECT * FROM orders ORDER BY created_at DESC LIMIT 3");
$recentMessages = fetchAll("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 4");
$recentApplications = fetchAll("SELECT ja.*, jp.title as job_title FROM job_applications ja JOIN job_positions jp ON ja.job_id = jp.id ORDER BY ja.created_at DESC LIMIT 2");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - DevGlobal</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h1>DevGlobal</h1>
                <p>Admin Panel</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="services.html">
                            <i class="fas fa-cogs"></i>
                            <span>Services</span>
                        </a>
                    </li>
                    <li>
                        <a href="pricing.html">
                            <i class="fas fa-tags"></i>
                            <span>Pricing Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="orders.html">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li>
                        <a href="hiring.html">
                            <i class="fas fa-users"></i>
                            <span>Hiring</span>
                        </a>
                    </li>
                    <li>
                        <a href="portfolio.html">
                            <i class="fas fa-briefcase"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Top Header -->
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search...">
                </div>
                <div class="header-actions">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="admin-profile">
                        <img src="https://placehold.co/100x100" alt="Admin">
                        <span><?php echo htmlspecialchars($currentAdmin['full_name'] ?: $currentAdmin['username']); ?></span>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <div class="page-header">
                    <h2>Dashboard</h2>
                    <p>Welcome to DevGlobal Admin Panel</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['new_orders']; ?></h3>
                            <p>New Orders</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">This week</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, $stats['new_orders'] * 10); ?>%;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['job_applications']; ?></h3>
                            <p>Job Applications</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">This week</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, $stats['job_applications'] * 15); ?>%;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo formatCurrency($stats['revenue']); ?></h3>
                            <p>Revenue</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">This month</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, ($stats['revenue'] / 1000) * 2); ?>%;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['active_projects']; ?></h3>
                            <p>Active Projects</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">Currently active</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, $stats['active_projects'] * 20); ?>%;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['contact_messages']; ?></h3>
                            <p>New Messages</p>
                        </div>
                        <div class="stat-progress">
                            <span class="progress-text">Unread</span>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?php echo min(100, $stats['contact_messages'] * 25); ?>%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h3>Recent Activity</h3>
                        <a href="#" class="view-all">View All</a>
                    </div>
                    <div class="activity-list">
                        <?php foreach ($recentMessages as $message): ?>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-comment"></i>
                            </div>
                            <div class="activity-details">
                                <h4>New Contact Message</h4>
                                <p><?php echo htmlspecialchars(truncateText($message['subject'], 50)); ?></p>
                                <span class="activity-time"><?php echo formatDate($message['created_at'], 'M d, Y g:i A'); ?></span>
                            </div>
                            <div class="activity-actions">
                                <a href="messages.php?id=<?php echo $message['id']; ?>" class="btn btn-sm">View</a>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <?php foreach ($recentApplications as $application): ?>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="activity-details">
                                <h4>New Job Application</h4>
                                <p><?php echo htmlspecialchars($application['job_title']); ?></p>
                                <span class="activity-time"><?php echo formatDate($application['created_at'], 'M d, Y g:i A'); ?></span>
                            </div>
                            <div class="activity-actions">
                                <a href="hiring.php?id=<?php echo $application['id']; ?>" class="btn btn-sm">View</a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Quick Actions and Recent Orders -->
                <div class="dashboard-columns">
                    <!-- Quick Actions -->
                    <div class="dashboard-column">
                        <div class="dashboard-section">
                            <div class="section-header">
                                <h3>Quick Actions</h3>
                            </div>
                            <div class="quick-actions">
                                <a href="services.html" class="quick-action">
                                    <i class="fas fa-plus-circle"></i>
                                    <span>Add Service</span>
                                </a>
                                <a href="pricing.html" class="quick-action">
                                    <i class="fas fa-tag"></i>
                                    <span>Update Pricing</span>
                                </a>
                                <a href="orders.html" class="quick-action">
                                    <i class="fas fa-clipboard-list"></i>
                                    <span>Process Orders</span>
                                </a>
                                <a href="portfolio.html" class="quick-action">
                                    <i class="fas fa-image"></i>
                                    <span>Add Portfolio Item</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Orders -->
                    <div class="dashboard-column">
                        <div class="dashboard-section">
                            <div class="section-header">
                                <h3>Recent Orders</h3>
                                <a href="orders.html" class="view-all">View All</a>
                            </div>
                            <div class="recent-orders">
                                <div class="data-table-container">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>Order ID</th>
                                                <th>Customer</th>
                                                <th>Service</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($recentOrders)): ?>
                                            <tr>
                                                <td colspan="4" style="text-align: center; color: #666;">No recent orders</td>
                                            </tr>
                                            <?php else: ?>
                                                <?php foreach ($recentOrders as $order): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                                                    <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                                    <td><?php echo htmlspecialchars(ucfirst(str_replace('-', ' ', $order['service_type']))); ?></td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo str_replace('-', '', $order['status']); ?>">
                                                            <?php echo ucfirst(str_replace('-', ' ', $order['status'])); ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>