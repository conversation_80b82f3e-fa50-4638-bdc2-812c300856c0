<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Plans - DevGlobal Admin</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h1>DevGlobal</h1>
                <p>Admin Panel</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="services.html">
                            <i class="fas fa-cogs"></i>
                            <span>Services</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="pricing.html">
                            <i class="fas fa-tags"></i>
                            <span>Pricing Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="orders.html">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li>
                        <a href="hiring.html">
                            <i class="fas fa-users"></i>
                            <span>Hiring</span>
                        </a>
                    </li>
                    <li>
                        <a href="portfolio.html">
                            <i class="fas fa-briefcase"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Top Header -->
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search plans...">
                </div>
                <div class="header-actions">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="admin-profile">
                        <img src="https://placehold.co/100x100" alt="Admin">
                        <span>Admin User</span>
                    </div>
                </div>
            </header>

            <!-- Pricing Plans Content -->
            <div class="dashboard-content">
                <div class="page-header">
                    <h2>Pricing Plans</h2>
                    <p>Manage your service pricing plans</p>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons" style="margin-bottom: 25px;">
                    <button class="btn btn-primary" id="addPlanBtn">
                        <i class="fas fa-plus"></i> Add New Plan
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-file-export"></i> Export Plans
                    </button>
                </div>

                <!-- Plans Categories Tabs -->
                <div class="tabs-container" style="margin-bottom: 25px;">
                    <div class="tabs">
                        <button class="tab active" data-category="all">All Plans</button>
                        <button class="tab" data-category="web">Web Development</button>
                        <button class="tab" data-category="app">App Development</button>
                        <button class="tab" data-category="marketing">Digital Marketing</button>
                        <button class="tab" data-category="hosting">Hosting</button>
                    </div>
                </div>

                <!-- Plans Table -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h3>All Pricing Plans</h3>
                        <div class="header-actions">
                            <select class="form-select" style="width: auto; padding: 8px 30px 8px 15px;">
                                <option>Sort by: Newest</option>
                                <option>Sort by: Oldest</option>
                                <option>Sort by: Price (Low to High)</option>
                                <option>Sort by: Price (High to Low)</option>
                            </select>
                        </div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Plan Name</th>
                                <th>Category</th>
                                <th>Building Cost</th>
                                <th>Monthly Fee</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>PL-001</td>
                                <td>Simple</td>
                                <td>Web Development</td>
                                <td>₹1,000</td>
                                <td>₹200</td>
                                <td><span class="status-badge status-completed">Active</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon edit-plan" data-id="PL-001">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-plan" data-id="PL-001">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>PL-002</td>
                                <td>Standard</td>
                                <td>Web Development</td>
                                <td>₹3,000</td>
                                <td>₹500</td>
                                <td><span class="status-badge status-completed">Active</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon edit-plan" data-id="PL-002">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-plan" data-id="PL-002">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>PL-003</td>
                                <td>Advanced</td>
                                <td>Web Development</td>
                                <td>₹5,000</td>
                                <td>₹1,000</td>
                                <td><span class="status-badge status-completed">Active</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon edit-plan" data-id="PL-003">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-plan" data-id="PL-003">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>PL-004</td>
                                <td>Basic Hosting</td>
                                <td>Hosting</td>
                                <td>₹500</td>
                                <td>₹100</td>
                                <td><span class="status-badge status-completed">Active</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon edit-plan" data-id="PL-004">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-plan" data-id="PL-004">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>PL-005</td>
                                <td>SEO Package</td>
                                <td>Digital Marketing</td>
                                <td>₹2,000</td>
                                <td>₹300</td>
                                <td><span class="status-badge status-pending">Draft</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon edit-plan" data-id="PL-005">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete-plan" data-id="PL-005">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination">
                        <button class="page-btn"><i class="fas fa-chevron-left"></i></button>
                        <button class="page-btn active">1</button>
                        <button class="page-btn">2</button>
                        <button class="page-btn">3</button>
                        <button class="page-btn"><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>

                <!-- Add/Edit Plan Modal -->
                <div class="modal" id="planModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Add New Plan</h3>
                            <button class="close-modal"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="modal-body">
                            <form class="admin-form" id="planForm">
                                <div class="form-row" style="display: flex; gap: 20px;">
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Plan Name</label>
                                        <input type="text" class="form-control" placeholder="Enter plan name">
                                    </div>
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Category</label>
                                        <select class="form-select">
                                            <option>Web Development</option>
                                            <option>App Development</option>
                                            <option>Digital Marketing</option>
                                            <option>Hosting</option>
                                            <option>Graphic Design</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-row" style="display: flex; gap: 20px;">
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Building Cost (₹)</label>
                                        <input type="number" class="form-control" placeholder="Enter building cost">
                                    </div>
                                    <div class="form-group" style="flex: 1;">
                                        <label class="form-label">Monthly Fee (₹)</label>
                                        <input type="number" class="form-control" placeholder="Enter monthly fee">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Description</label>
                                    <textarea class="form-control" placeholder="Enter plan description"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Features (one per line)</label>
                                    <textarea class="form-control" placeholder="Enter features, one per line"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Status</label>
                                    <div class="form-check">
                                        <input type="radio" name="status" id="statusActive" class="form-check-input" checked>
                                        <label for="statusActive">Active</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" name="status" id="statusDraft" class="form-check-input">
                                        <label for="statusDraft">Draft</label>
                                    </div>
                                </div>
                                
                                <div class="form-buttons">
                                    <button type="submit" class="btn btn-primary">Save Plan</button>
                                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Modal functionality
        const modal = document.getElementById('planModal');
        const addPlanBtn = document.getElementById('addPlanBtn');
        const closeModalBtns = document.querySelectorAll('.close-modal');
        const editPlanBtns = document.querySelectorAll('.edit-plan');
        
        addPlanBtn.addEventListener('click', () => {
            modal.style.display = 'flex';
        });
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        });
        
        editPlanBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // In a real application, you would fetch the plan data here
                document.querySelector('.modal-header h3').textContent = 'Edit Plan';
                modal.style.display = 'flex';
            });
        });
        
        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
        
        // Tab functionality
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                // In a real application, you would filter the plans here
            });
        });
    </script>
</body>
</html> -->