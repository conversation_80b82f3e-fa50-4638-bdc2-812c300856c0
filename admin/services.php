<?php
require_once '../config/config.php';

// Check admin authentication
requireAdminLogin();

// Get current admin
$currentAdmin = getCurrentAdmin();

// Get all services
$services = fetchAll("SELECT * FROM services ORDER BY sort_order ASC, id ASC");

// Decode JSON fields
foreach ($services as &$service) {
    $service['features'] = json_decode($service['features'] ?? '[]', true);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services Management - DevGlobal Admin</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .service-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }
        
        .service-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .service-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .service-title {
            flex: 1;
        }
        
        .service-title h3 {
            margin: 0 0 5px 0;
            font-size: 1.1rem;
        }
        
        .service-price {
            font-weight: 600;
            color: var(--primary);
        }
        
        .service-description {
            margin-bottom: 15px;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .service-features {
            margin-bottom: 15px;
        }
        
        .service-features h4 {
            font-size: 0.9rem;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .features-list li {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-bottom: 3px;
            padding-left: 15px;
            position: relative;
        }
        
        .features-list li:before {
            content: '•';
            color: var(--primary);
            position: absolute;
            left: 0;
        }
        
        .service-actions {
            display: flex;
            gap: 10px;
        }
        
        .service-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
        
        .add-service-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
            transition: var(--transition-fast);
        }
        
        .add-service-btn:hover {
            background: var(--secondary);
            transform: scale(1.1);
        }
    </style>
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h1>DevGlobal</h1>
                <p>Admin Panel</p>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.php">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="services.php">
                            <i class="fas fa-cogs"></i>
                            <span>Services</span>
                        </a>
                    </li>
                    <li>
                        <a href="pricing.php">
                            <i class="fas fa-tags"></i>
                            <span>Pricing Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="orders.php">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li>
                        <a href="hiring.php">
                            <i class="fas fa-users"></i>
                            <span>Hiring</span>
                        </a>
                    </li>
                    <li>
                        <a href="portfolio.php">
                            <i class="fas fa-briefcase"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.php">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <!-- Top Header -->
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search services...">
                </div>
                <div class="header-actions">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="admin-profile">
                        <img src="https://placehold.co/100x100" alt="Admin">
                        <span><?php echo htmlspecialchars($currentAdmin['full_name'] ?: $currentAdmin['username']); ?></span>
                    </div>
                </div>
            </header>

            <!-- Services Content -->
            <div class="dashboard-content">
                <div class="page-header">
                    <h2>Services Management</h2>
                    <p>Manage your services and their details</p>
                </div>

                <div class="services-grid">
                    <?php foreach ($services as $service): ?>
                    <div class="service-card">
                        <div class="service-header">
                            <div class="service-icon">
                                <i class="<?php echo htmlspecialchars($service['icon'] ?: 'fas fa-cog'); ?>"></i>
                            </div>
                            <div class="service-title">
                                <h3><?php echo htmlspecialchars($service['title']); ?></h3>
                                <?php if ($service['price'] > 0): ?>
                                <div class="service-price">
                                    <?php echo formatCurrency($service['price']); ?>
                                    <?php if ($service['price_type'] !== 'one-time'): ?>
                                        /<?php echo $service['price_type'] === 'monthly' ? 'month' : 'year'; ?>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <span class="service-status status-<?php echo $service['is_active'] ? 'active' : 'inactive'; ?>">
                                <?php echo $service['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                        </div>
                        
                        <div class="service-description">
                            <?php echo htmlspecialchars(truncateText($service['description'], 120)); ?>
                        </div>
                        
                        <?php if (!empty($service['features'])): ?>
                        <div class="service-features">
                            <h4>Features:</h4>
                            <ul class="features-list">
                                <?php foreach (array_slice($service['features'], 0, 3) as $feature): ?>
                                <li><?php echo htmlspecialchars($feature); ?></li>
                                <?php endforeach; ?>
                                <?php if (count($service['features']) > 3): ?>
                                <li>... and <?php echo count($service['features']) - 3; ?> more</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                        
                        <div class="service-actions">
                            <button class="btn btn-sm btn-primary" onclick="editService(<?php echo $service['id']; ?>)">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="toggleService(<?php echo $service['id']; ?>, <?php echo $service['is_active'] ? 'false' : 'true'; ?>)">
                                <i class="fas fa-<?php echo $service['is_active'] ? 'eye-slash' : 'eye'; ?>"></i>
                                <?php echo $service['is_active'] ? 'Disable' : 'Enable'; ?>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteService(<?php echo $service['id']; ?>)">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- Add Service Button -->
    <button class="add-service-btn" onclick="addService()" title="Add New Service">
        <i class="fas fa-plus"></i>
    </button>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        function addService() {
            // This would open a modal or redirect to add service page
            alert('Add Service functionality - would open a form modal');
        }
        
        function editService(id) {
            // This would open edit modal or redirect to edit page
            alert('Edit Service ID: ' + id + ' - would open edit form');
        }
        
        function toggleService(id, newStatus) {
            if (confirm('Are you sure you want to ' + (newStatus === 'true' ? 'enable' : 'disable') + ' this service?')) {
                // Make API call to toggle service status
                fetch('../api/services.php?id=' + id, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        is_active: newStatus === 'true'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error updating service');
                });
            }
        }
        
        function deleteService(id) {
            if (confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
                // Make API call to delete service
                fetch('../api/services.php?id=' + id, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error deleting service');
                });
            }
        }
    </script>
</body>
</html>
