/* 
   DevGlobal - Admin Panel Stylesheet
   This file contains all styles specific to the admin panel
*/

/* ===== Admin Layout ===== */
.admin-body {
    background-color: #f5f7fa;
    min-height: 100vh;
    overflow-x: hidden;
}

.admin-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 1.2rem;
    transition: var(--transition-fast);
    margin-right: 15px;
}

.menu-toggle:hover {
    background-color: var(--primary-dark);
}

@media (max-width: 992px) {
    .menu-toggle {
        display: flex;
    }
}

/* Sidebar overlay for mobile */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 90;
}

/* We don't want to show the overlay when sidebar is collapsed */
.sidebar-collapsed .sidebar-overlay {
    display: none;
}

/* Ensure proper z-index for dropdowns */
.dropdown-content {
    z-index: 1050;
}

@media (min-width: 993px) {
    .menu-toggle {
        display: none;
    }
}

/* ===== Sidebar ===== */
.admin-sidebar {
    width: 280px;
    background-color: var(--bg-dark);
    color: var(--text-light);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 100;
    transition: var(--transition-fast);
    left: 0;
}

.sidebar-header {
    padding: 25px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    color: var(--text-light);
}

.sidebar-header p {
    font-size: 0.9rem;
    opacity: 0.7;
    margin: 0;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition-fast);
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover,
.sidebar-nav li.active a {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    border-left-color: var(--primary);
}

.sidebar-nav a i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition-fast);
    padding: 10px;
    border-radius: var(--border-radius-md);
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

.logout-btn i {
    margin-right: 10px;
}

/* ===== Main Content ===== */
.admin-main {
    flex: 1;
    margin-left: 280px;
    padding: 20px;
    transition: var(--transition-fast);
    width: calc(100% - 280px);
    overflow-x: hidden;
}

@media (max-width: 768px) {
    .admin-main {
        padding: 15px;
    }
}

@media (max-width: 576px) {
    .admin-main {
        padding: 10px;
    }
}

/* ===== Header ===== */
.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: 25px;
}

.header-search {
    position: relative;
    width: 300px;
}

.header-search input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-round);
    font-family: 'Poppins', sans-serif;
    transition: var(--transition-fast);
}

.header-search input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.header-search i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.notifications {
    position: relative;
    cursor: pointer;
}

.notifications i {
    font-size: 1.2rem;
    color: var(--text-secondary);
}

/* Notification Dropdown Styles */
.dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    z-index: 1050;
    display: none;
    overflow: hidden;
    margin-top: 10px;
}

.dropdown-content.show {
    display: block;
}

.dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
}

.dropdown-header h4 {
    margin: 0;
    font-size: 1rem;
}

.mark-all-read {
    font-size: 0.8rem;
    color: var(--primary);
    text-decoration: none;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition-fast);
}

.notification-item:hover {
    background-color: var(--bg-secondary);
}

.notification-item.unread {
    background-color: rgba(67, 97, 238, 0.05);
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: var(--primary);
}

.notification-content {
    flex: 1;
}

.notification-text {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
}

.notification-time {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.dropdown-footer {
    padding: 12px 15px;
    text-align: center;
    border-top: 1px solid var(--border-color);
}

.dropdown-footer a {
    color: var(--primary);
    text-decoration: none;
    font-size: 0.9rem;
}

.badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--danger);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.admin-profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* ===== Dashboard Content ===== */
.dashboard-content {
    padding: 20px;
}

.page-header {
    margin-bottom: 30px;
}

.page-header h2 {
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.page-header p {
    color: var(--text-secondary);
    margin: 0;
}

/* ===== Stats Cards ===== */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

@media (max-width: 1200px) {
    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }
}

.stat-card {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.stat-details h3 {
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.stat-details p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.stat-progress {
    margin-top: 15px;
}

.progress-text {
    display: block;
    font-size: 0.8rem;
    color: var(--success);
    margin-bottom: 5px;
}

.progress-bar {
    height: 6px;
    background-color: rgba(67, 97, 238, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
}

/* ===== Dashboard Sections ===== */
.dashboard-section {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    margin-bottom: 25px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 1.3rem;
    margin: 0;
}

.view-all {
    color: var(--primary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.view-all:hover {
    text-decoration: underline;
}

/* ===== Activity List ===== */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: var(--border-radius-md);
    background-color: var(--bg-secondary);
    transition: var(--transition-fast);
}

.activity-item:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

@media (max-width: 576px) {
    .activity-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .activity-icon {
        margin-bottom: 10px;
    }
    
    .activity-actions {
        margin-top: 10px;
        align-self: flex-end;
    }
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background-color: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary);
    font-size: 1rem;
    box-shadow: var(--shadow-sm);
}

.activity-details {
    flex: 1;
}

.activity-details h4 {
    font-size: 1rem;
    margin-bottom: 3px;
}

.activity-details p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
    display: block;
    margin-top: 5px;
}

.activity-actions .btn-sm {
    padding: 5px 15px;
    font-size: 0.8rem;
}

/* ===== Dashboard Columns ===== */
.dashboard-columns {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 25px;
}

@media (max-width: 768px) {
    .dashboard-columns {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* ===== Quick Actions ===== */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

@media (max-width: 576px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }
}

.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition-fast);
    text-align: center;
}

.quick-action:hover {
    background-color: var(--primary);
    color: white;
    transform: translateY(-3px);
}

.quick-action i {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

/* ===== Data Tables ===== */
.data-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.data-table-container {
    overflow-x: auto;
    margin-bottom: 20px;
    width: 100%;
    -webkit-overflow-scrolling: touch;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

@media (max-width: 576px) {
    .data-table th,
    .data-table td {
        padding: 10px 8px;
        font-size: 0.9rem;
    }
    
    .data-table th {
        font-size: 0.85rem;
    }
}

.data-table th {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.data-table tbody tr {
    transition: var(--transition-fast);
}

.data-table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

.status-badge {
    display: inline-block;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-pending {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning);
}

.status-progress {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary);
}

.status-completed {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success);
}

.status-cancelled {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger);
}

/* ===== Forms ===== */
.admin-form {
    max-width: 800px;
    width: 100%;
}

.form-group {
    margin-bottom: 20px;
}

@media (max-width: 576px) {
    .admin-form {
        padding: 0 10px;
    }
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-family: 'Poppins', sans-serif;
    transition: var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

.form-select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-family: 'Poppins', sans-serif;
    transition: var(--transition-fast);
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23666' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.form-check-input {
    margin-right: 10px;
}

.form-buttons {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

@media (max-width: 576px) {
    .form-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .form-buttons .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* ===== Responsive ===== */
@media (max-width: 992px) {
    .admin-sidebar {
        transform: translateX(-100%);
        width: 280px;
        z-index: 1100;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    }
    
    .admin-container:not(.sidebar-collapsed) .admin-sidebar {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
        width: 100%;
        position: relative;
        z-index: 80;
    }
    
    .dashboard-columns {
        grid-template-columns: 1fr;
    }
    
    /* Create overlay for sidebar on mobile */
    .sidebar-overlay {
        display: none;
    }
    
    /* Only show overlay when sidebar is visible on mobile */
    .admin-container:not(.sidebar-collapsed) .sidebar-overlay {
        display: block;
        z-index: 90;
    }
    
    /* Fix dropdown positioning on mobile */
    .dropdown-content {
        position: fixed;
        width: 300px;
        right: 10px;
        max-height: 80vh;
        overflow-y: auto;
    }
    
    .sidebar-nav a {
        padding: 15px 20px;
    }
    
    .sidebar-nav a i {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .header-search {
        width: 200px;
    }
    
    .dropdown-content {
        width: 280px;
        right: -70px;
    }
    
    .dropdown-content:before {
        right: 80px;
    }
}

@media (max-width: 576px) {
    .admin-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
        padding: 15px 10px;
        position: relative;
        z-index: 85;
    }
    
    .header-search {
        width: 100%;
    }
    
    .header-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .dropdown-content {
        width: 100%;
        right: 0;
        position: fixed;
        left: 0;
        margin-top: 5px;
        max-height: 80vh;
        overflow-y: auto;
        border-radius: 0;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    
    .notifications {
        position: static;
    }
    
    .notifications .badge {
        position: absolute;
        top: 5px;
        right: 5px;
    }
    
    .stat-card {
        overflow: hidden;
    }
}