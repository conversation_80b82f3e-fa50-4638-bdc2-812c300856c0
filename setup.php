<?php
/**
 * DevGlobal Setup Script
 * This script helps initialize the database and check system requirements
 */

// Check if setup is already completed
if (file_exists('config/setup_complete.txt')) {
    die('Setup has already been completed. Delete config/setup_complete.txt to run setup again.');
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 2) {
        // Database configuration step
        $host = $_POST['db_host'] ?? 'localhost';
        $name = $_POST['db_name'] ?? 'devglobal_db';
        $user = $_POST['db_user'] ?? 'root';
        $pass = $_POST['db_pass'] ?? '';
        
        // Test database connection
        try {
            $dsn = "mysql:host=$host;charset=utf8mb4";
            $pdo = new PDO($dsn, $user, $pass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Update config file
            $configContent = file_get_contents('config/database.php');
            $configContent = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$host');", $configContent);
            $configContent = str_replace("define('DB_NAME', 'devglobal_db');", "define('DB_NAME', '$name');", $configContent);
            $configContent = str_replace("define('DB_USER', 'root');", "define('DB_USER', '$user');", $configContent);
            $configContent = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$pass');", $configContent);
            
            file_put_contents('config/database.php', $configContent);
            
            $success = 'Database configuration saved successfully!';
            $step = 3;
        } catch (Exception $e) {
            $error = 'Database connection failed: ' . $e->getMessage();
        }
    } elseif ($step == 3) {
        // Import database schema
        try {
            require_once 'config/database.php';
            
            $sql = file_get_contents('database/devglobal_database.sql');
            $db = getDB();
            
            // Execute SQL statements
            $statements = explode(';', $sql);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $db->exec($statement);
                }
            }
            
            $success = 'Database tables created successfully!';
            $step = 4;
        } catch (Exception $e) {
            $error = 'Failed to create database tables: ' . $e->getMessage();
        }
    } elseif ($step == 4) {
        // Admin user setup
        $username = $_POST['admin_username'] ?? '';
        $email = $_POST['admin_email'] ?? '';
        $password = $_POST['admin_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $full_name = $_POST['full_name'] ?? '';
        
        if (empty($username) || empty($email) || empty($password)) {
            $error = 'Please fill in all required fields.';
        } elseif ($password !== $confirm_password) {
            $error = 'Passwords do not match.';
        } elseif (strlen($password) < 6) {
            $error = 'Password must be at least 6 characters long.';
        } else {
            try {
                require_once 'config/database.php';
                
                // Check if admin already exists
                $existing = fetchOne("SELECT id FROM admin_users WHERE username = :username OR email = :email", 
                                   ['username' => $username, 'email' => $email]);
                
                if ($existing) {
                    $error = 'Admin user with this username or email already exists.';
                } else {
                    // Create admin user
                    $data = [
                        'username' => $username,
                        'email' => $email,
                        'password_hash' => hashPassword($password),
                        'full_name' => $full_name,
                        'role' => 'super_admin'
                    ];
                    
                    $adminId = insertData('admin_users', $data);
                    
                    if ($adminId) {
                        // Mark setup as complete
                        file_put_contents('config/setup_complete.txt', date('Y-m-d H:i:s'));
                        
                        $success = 'Setup completed successfully! You can now login to the admin panel.';
                        $step = 5;
                    } else {
                        $error = 'Failed to create admin user.';
                    }
                }
            } catch (Exception $e) {
                $error = 'Error creating admin user: ' . $e->getMessage();
            }
        }
    }
}

// Check system requirements
function checkRequirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'JSON Extension' => extension_loaded('json'),
        'Config Directory Writable' => is_writable('config'),
        'Upload Directory Exists' => is_dir('uploads') || mkdir('uploads', 0755, true)
    ];
    
    return $requirements;
}

$requirements = checkRequirements();
$allRequirementsMet = !in_array(false, $requirements);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevGlobal Setup</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .setup-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            padding: 20px;
        }
        
        .setup-box {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: var(--shadow-lg);
            width: 100%;
            max-width: 600px;
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .setup-header h1 {
            color: var(--primary);
            margin-bottom: 10px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        
        .step.active {
            background: var(--primary);
            color: white;
        }
        
        .step.completed {
            background: var(--success);
            color: white;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 5px;
            font-size: 16px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
        }
        
        .btn-success {
            background: var(--success);
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .requirements-list {
            list-style: none;
            padding: 0;
        }
        
        .requirements-list li {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .requirement-pass {
            background: #d4edda;
            color: #155724;
        }
        
        .requirement-fail {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-box">
            <div class="setup-header">
                <h1><i class="fas fa-cog"></i> DevGlobal Setup</h1>
                <p>Welcome! Let's set up your DevGlobal website.</p>
            </div>
            
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? ($step > 4 ? 'completed' : 'active') : ''; ?>">4</div>
                <div class="step <?php echo $step >= 5 ? 'active' : ''; ?>">5</div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <!-- Step 1: System Requirements -->
                <h3>Step 1: System Requirements</h3>
                <p>Checking if your system meets the requirements...</p>
                
                <ul class="requirements-list">
                    <?php foreach ($requirements as $requirement => $met): ?>
                    <li class="<?php echo $met ? 'requirement-pass' : 'requirement-fail'; ?>">
                        <span><?php echo $requirement; ?></span>
                        <span>
                            <?php if ($met): ?>
                                <i class="fas fa-check"></i> Pass
                            <?php else: ?>
                                <i class="fas fa-times"></i> Fail
                            <?php endif; ?>
                        </span>
                    </li>
                    <?php endforeach; ?>
                </ul>
                
                <?php if ($allRequirementsMet): ?>
                    <a href="?step=2" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> Continue to Database Setup
                    </a>
                <?php else: ?>
                    <p style="color: var(--danger); margin-top: 20px;">
                        <i class="fas fa-exclamation-triangle"></i> 
                        Please fix the failed requirements before continuing.
                    </p>
                <?php endif; ?>
                
            <?php elseif ($step == 2): ?>
                <!-- Step 2: Database Configuration -->
                <h3>Step 2: Database Configuration</h3>
                <p>Enter your database connection details:</p>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="db_host">Database Host</label>
                        <input type="text" id="db_host" name="db_host" class="form-control" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label for="db_name">Database Name</label>
                        <input type="text" id="db_name" name="db_name" class="form-control" value="devglobal_db" required>
                    </div>
                    <div class="form-group">
                        <label for="db_user">Database Username</label>
                        <input type="text" id="db_user" name="db_user" class="form-control" value="root" required>
                    </div>
                    <div class="form-group">
                        <label for="db_pass">Database Password</label>
                        <input type="password" id="db_pass" name="db_pass" class="form-control">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-database"></i> Test Connection & Save
                    </button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- Step 3: Database Import -->
                <h3>Step 3: Database Setup</h3>
                <p>Now we'll create the database tables and import sample data.</p>
                
                <form method="POST">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download"></i> Create Database Tables
                    </button>
                </form>
                
            <?php elseif ($step == 4): ?>
                <!-- Step 4: Admin User -->
                <h3>Step 4: Create Admin User</h3>
                <p>Create your admin account to manage the website:</p>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="admin_username">Username *</label>
                        <input type="text" id="admin_username" name="admin_username" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="admin_email">Email Address *</label>
                        <input type="email" id="admin_email" name="admin_email" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="full_name">Full Name</label>
                        <input type="text" id="full_name" name="full_name" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="admin_password">Password *</label>
                        <input type="password" id="admin_password" name="admin_password" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">Confirm Password *</label>
                        <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Create Admin User
                    </button>
                </form>
                
            <?php elseif ($step == 5): ?>
                <!-- Step 5: Complete -->
                <h3>Setup Complete!</h3>
                <p>Congratulations! Your DevGlobal website has been set up successfully.</p>
                
                <div style="margin: 30px 0;">
                    <h4>What's Next?</h4>
                    <ul>
                        <li>Visit your <a href="index.php">website homepage</a></li>
                        <li>Login to the <a href="admin/login.php">admin panel</a></li>
                        <li>Customize your site settings</li>
                        <li>Add your services and content</li>
                    </ul>
                </div>
                
                <div style="display: flex; gap: 15px;">
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home"></i> View Website
                    </a>
                    <a href="admin/login.php" class="btn btn-success">
                        <i class="fas fa-sign-in-alt"></i> Admin Login
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
