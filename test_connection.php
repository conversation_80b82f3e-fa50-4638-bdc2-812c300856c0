<?php
/**
 * Database Connection Test Script
 * Run this to verify your database setup is working correctly
 */

// Include configuration
require_once 'config/config.php';

echo "<h1>DevGlobal Database Connection Test</h1>";

try {
    // Test database connection
    $db = getDB();
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Test if tables exist
    $tables = [
        'site_settings',
        'services', 
        'hosting_plans',
        'development_plans',
        'portfolio_items',
        'job_positions',
        'contact_messages',
        'orders',
        'job_applications',
        'admin_users',
        'gps_products'
    ];
    
    echo "<h2>Table Status:</h2>";
    echo "<ul>";
    
    foreach ($tables as $table) {
        try {
            $result = $db->query("SELECT COUNT(*) FROM $table");
            $count = $result->fetchColumn();
            echo "<li style='color: green;'>✓ Table '$table' exists with $count records</li>";
        } catch (Exception $e) {
            echo "<li style='color: red;'>✗ Table '$table' missing or error: " . $e->getMessage() . "</li>";
        }
    }
    
    echo "</ul>";
    
    // Test site settings
    echo "<h2>Site Settings Test:</h2>";
    $settings = getSiteSettings();
    if ($settings) {
        echo "<p style='color: green;'>✓ Site settings loaded successfully</p>";
        echo "<ul>";
        echo "<li>Site Name: " . htmlspecialchars($settings['site_name']) . "</li>";
        echo "<li>Contact Email: " . htmlspecialchars($settings['contact_email']) . "</li>";
        echo "<li>Contact Phone: " . htmlspecialchars($settings['contact_phone']) . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ Failed to load site settings</p>";
    }
    
    // Test admin user
    echo "<h2>Admin User Test:</h2>";
    $admin = fetchOne("SELECT username, email, role FROM admin_users WHERE role = 'super_admin' LIMIT 1");
    if ($admin) {
        echo "<p style='color: green;'>✓ Admin user found</p>";
        echo "<ul>";
        echo "<li>Username: " . htmlspecialchars($admin['username']) . "</li>";
        echo "<li>Email: " . htmlspecialchars($admin['email']) . "</li>";
        echo "<li>Role: " . htmlspecialchars($admin['role']) . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ No admin user found</p>";
    }
    
    // Test services
    echo "<h2>Services Test:</h2>";
    $services = fetchAll("SELECT title, price, is_active FROM services LIMIT 3");
    if ($services) {
        echo "<p style='color: green;'>✓ Services loaded successfully</p>";
        echo "<ul>";
        foreach ($services as $service) {
            $status = $service['is_active'] ? 'Active' : 'Inactive';
            echo "<li>" . htmlspecialchars($service['title']) . " - " . formatCurrency($service['price']) . " ($status)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠ No services found</p>";
    }
    
    // Test API endpoints
    echo "<h2>API Endpoints Test:</h2>";
    $apiEndpoints = [
        'api/services.php',
        'api/contact.php', 
        'api/settings.php',
        'api/pricing.php'
    ];
    
    echo "<ul>";
    foreach ($apiEndpoints as $endpoint) {
        if (file_exists($endpoint)) {
            echo "<li style='color: green;'>✓ $endpoint exists</li>";
        } else {
            echo "<li style='color: red;'>✗ $endpoint missing</li>";
        }
    }
    echo "</ul>";
    
    // Test file permissions
    echo "<h2>File Permissions Test:</h2>";
    echo "<ul>";
    
    if (is_writable('config/')) {
        echo "<li style='color: green;'>✓ Config directory is writable</li>";
    } else {
        echo "<li style='color: red;'>✗ Config directory is not writable</li>";
    }
    
    if (is_dir('uploads/')) {
        if (is_writable('uploads/')) {
            echo "<li style='color: green;'>✓ Uploads directory exists and is writable</li>";
        } else {
            echo "<li style='color: orange;'>⚠ Uploads directory exists but is not writable</li>";
        }
    } else {
        if (mkdir('uploads/', 0755, true)) {
            echo "<li style='color: green;'>✓ Uploads directory created successfully</li>";
        } else {
            echo "<li style='color: red;'>✗ Failed to create uploads directory</li>";
        }
    }
    
    echo "</ul>";
    
    echo "<h2>Summary:</h2>";
    echo "<p style='color: green; font-weight: bold;'>✓ Database setup appears to be working correctly!</p>";
    echo "<p>You can now:</p>";
    echo "<ul>";
    echo "<li><a href='index.php'>Visit your website homepage</a></li>";
    echo "<li><a href='admin/login.php'>Login to the admin panel</a></li>";
    echo "<li><a href='setup.php'>Run the setup wizard</a> (if not completed)</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in config/database.php</p>";
    echo "<p>Make sure:</p>";
    echo "<ul>";
    echo "<li>MySQL server is running</li>";
    echo "<li>Database credentials are correct</li>";
    echo "<li>Database exists</li>";
    echo "<li>PHP PDO MySQL extension is installed</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><small>DevGlobal Database Test - " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

ul {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #4361ee;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 1px solid #ddd;
}
</style>
